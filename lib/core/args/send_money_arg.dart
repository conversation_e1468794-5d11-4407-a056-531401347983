import 'package:korrency/core/core.dart';

class SendMoneyArg {
  SendMoneyArg({
    required this.fromCurrencyCode,
    required this.toCurrencyCode,
    this.fromWallet,
    this.fromAmount,
    this.beneficiary,
    this.purpose,
  });

  final String fromCurrencyCode;
  final String toCurrencyCode;

  /// Optional, just for the wallet details
  final Wallet? fromWallet;
  final String? fromAmount;
  final Beneficiary? beneficiary;
  final SendMoneyPurpose? purpose;
}
