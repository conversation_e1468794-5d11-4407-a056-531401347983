import 'dart:convert';

import 'package:korrency/core/core.dart';

const String verifyMobileMoneyState = "verifyMobileMoneyState";

class BeneficiaryVM extends BaseVM {
  TextEditingController bankNameC = TextEditingController();
  TextEditingController accountNumC = TextEditingController();
  TextEditingController accountNameC = TextEditingController();

  // TextEditingController nameC = TextEditingController();
  TextEditingController fNameC = TextEditingController();
  TextEditingController lNameC = TextEditingController();
  TextEditingController emailC = TextEditingController();

  List<Beneficiary> _beneficiariesStore = [];
  List<Beneficiary> _beneficiaries = [];
  List<Beneficiary> get beneficiaries => _beneficiaries;
  List<Beneficiary> _beneficiariesByCurrencyId = [];
  List<Beneficiary> _beneficiariesByCurrencyIdStore = [];
  List<Beneficiary> get beneficiariesByCurrencyId => _beneficiariesByCurrencyId;
  String? _bankUUID;
  String? get bankUUID => _bankUUID;
  bool _isValidEmail = false;
  bool get isValidEmail => _isValidEmail;

  VerifyBank? _verifiedBank;

  bool get ngnButtonIsActive =>
      bankNameC.text.isNotEmpty && accountNumC.text.isNotEmpty;
  // &&
  // accountNameC.text.isNotEmpty;

  bool get cadButtonIsActive =>
      fNameC.text.isNotEmpty && lNameC.text.isNotEmpty && _isValidEmail;

  emailIsValid() {
    _isValidEmail = emailC.text.isNotEmpty &&
        emailC.text.contains("@") &&
        emailC.text.contains(".");
    reBuildUI();
  }

  // Search Beneficiaries
  searchBeneficiaries(String query) {
    if (query.isEmpty) {
      _beneficiaries = _beneficiariesStore;
    } else {
      _beneficiaries = _beneficiaries
          .where((element) =>
              element.accountName!.toLowerCase().contains(query.toLowerCase()))
          .toList();
    }
    reBuildUI();
  }

  // Search Home Frequent Beneficiaries
  searchHomeFreqBeneficiaries(String query) {
    if (query.isEmpty) {
      _filteredHomeFreqBeneficiaries = [];
    } else {
      _filteredHomeFreqBeneficiaries =
          _homeFreqBeneficiariesStore.where((element) {
        final accountName = element.accountName?.toLowerCase() ?? '';
        final accountIdentifier =
            element.accountIdentifier?.toLowerCase() ?? '';
        final institutionName = element.institutionName?.toLowerCase() ?? '';
        final searchQuery = query.toLowerCase();

        return accountName.contains(searchQuery) ||
            accountIdentifier.contains(searchQuery) ||
            institutionName.contains(searchQuery);
      }).toList();
    }
    reBuildUI();
  }

  // Search Beneficiaries By Currency Id
  searchBeneficiariesByCurrencyId(String query) {
    if (query.isEmpty) {
      _beneficiariesByCurrencyId = _beneficiariesByCurrencyIdStore;
    } else {
      _beneficiariesByCurrencyId =
          _beneficiariesByCurrencyIdStore.where((element) {
        final accountName = element.accountName?.toLowerCase() ?? '';
        final accountIdentifier =
            element.accountIdentifier?.toLowerCase() ?? '';
        final institutionName = element.institutionName?.toLowerCase() ?? '';
        final searchQuery = query.toLowerCase();

        return accountName.contains(searchQuery) ||
            accountIdentifier.contains(searchQuery) ||
            institutionName.contains(searchQuery);
      }).toList();
    }
    reBuildUI();
  }

  setBank(Bank bank) {
    printty(bank.toString(), level: 'set bank');
    bankNameC.text = bank.name!;
    _bankUUID = bank.uuid;

    reBuildUI();
  }

  Future<ApiResponse> getBeneficiaries({
    int? currencyId,
    String? transferMethod,
  }) async {
    UriBuilder uriBuilder = UriBuilder("/beneficiaries")
      ..addQueryParameterIfNotEmpty("currency_id", currencyId?.toString() ?? "")
      ..addQueryParameterIfNotEmpty("transfer_method", transferMethod ?? "");
    _beneficiariesByCurrencyId = [];
    return await performApiCall(
      url: uriBuilder.build().toString(),
      method: apiService.getWithAuth,
      onSuccess: (data) {
        if (currencyId != null) {
          var res = beneficiaryFromJson(json.encode(data["data"]));
          _beneficiariesByCurrencyId = res;
          _beneficiariesByCurrencyIdStore = res;
        } else {
          var res = beneficiaryFromJson(json.encode(data["data"]));
          _beneficiaries = res;
          _beneficiariesStore = res;
        }
        return apiResponse;
      },
    );
  }

  // For Home screen
  List<Beneficiary> _homeFreqBeneficiaries = [];
  List<Beneficiary> _homeFreqBeneficiariesStore = [];
  List<Beneficiary> _filteredHomeFreqBeneficiaries = [];
  List<Beneficiary> get homeFreqBeneficiaries =>
      _filteredHomeFreqBeneficiaries.isEmpty
          ? _homeFreqBeneficiaries
          : _filteredHomeFreqBeneficiaries;
  Future<ApiResponse> getFreqBeneficiaries({
    int? currencyId,
    String? transferMethod,
  }) async {
    UriBuilder uriBuilder = UriBuilder("/beneficiaries/frequent")
      ..addQueryParameterIfNotEmpty("currency_id", currencyId?.toString() ?? "")
      ..addQueryParameterIfNotEmpty("transfer_method", transferMethod ?? "");

    return await performApiCall(
      url: uriBuilder.build().toString(),
      method: apiService.getWithAuth,
      onSuccess: (data) {
        _homeFreqBeneficiaries = beneficiaryFromJson(json.encode(data["data"]));
        _homeFreqBeneficiariesStore =
            beneficiaryFromJson(json.encode(data["data"]));
        _filteredHomeFreqBeneficiaries = [];
        return apiResponse;
      },
    );
  }

  // For Wallet screens
  List<Beneficiary> _walletFreqBeneficiaries = [];
  List<Beneficiary> get walletFreqBeneficiaries => _walletFreqBeneficiaries;
  Future<ApiResponse> getWalletFreqBeneficiaries({
    required int currencyId,
    String? transferMethod,
  }) async {
    UriBuilder uriBuilder = UriBuilder("/beneficiaries/frequent")
      ..addQueryParameterIfNotEmpty("currency_id", currencyId.toString())
      ..addQueryParameterIfNotEmpty("transfer_method", transferMethod ?? "");

    return await performApiCall(
      url: uriBuilder.build().toString(),
      method: apiService.getWithAuth,
      onSuccess: (data) {
        _walletFreqBeneficiaries =
            beneficiaryFromJson(json.encode(data["data"]));
        return apiResponse;
      },
    );
  }

  Future<ApiResponse> deleteBeneficiary(int id) async {
    return await performApiCall(
      url: "/beneficiaries/$id",
      method: apiService.deleteWithAuth,
      onSuccess: (data) {
        getBeneficiaries();
        return ApiResponse(success: true, message: data['message']);
      },
    );
  }

  Future<ApiResponse> saveBeneficiary({
    required PostBeneficiaryArgs arg,
    TransferMethodType transferMethodType = TransferMethodType.bankTransfer,
  }) async {
    Map<String, Object?> body;
    switch (transferMethodType) {
      case TransferMethodType.bankTransfer:
        body = {
          "currency_id": arg.currencyId,
          "institution_name": arg.institutionName,
          "institution_code": arg.institutionCode,
          "account_name": arg.accountName,
          "account_identifier": arg.accountIdentifier,
          "transfer_method": arg.transferMethod,
          "first_name": arg.firstName,
          "last_name": arg.lastName,
        };
        break;
      case TransferMethodType.interac:
        body = {
          "currency_id": arg.currencyId,
          "first_name": arg.firstName,
          "last_name": arg.lastName,
          "account_identifier": arg.accountIdentifier,
        };
        break;
      case TransferMethodType.mobileMoney:
        body = {
          "currency_id": arg.currencyId,
          "institution_name": arg.institutionName,
          "institution_code": arg.institutionCode,
          "account_name": arg.accountName,
          "account_identifier": arg.accountIdentifier,
          "transfer_method": arg.transferMethod,
          "first_name": arg.firstName,
          "last_name": arg.lastName,
        };
        break;
      case TransferMethodType.iban:
        body = {
          "currency_id": arg.currencyId,
          "institution_name": arg.institutionName,
          "institution_code": arg.institutionCode,
          "account_name": arg.accountName,
          "account_identifier": arg.accountIdentifier,
          "transfer_method": arg.transferMethod,
          "first_name": arg.firstName,
          "last_name": arg.lastName,
        };
        break;
      default:
        throw Exception("Unsupported currency type");
    }
    return await performApiCall(
      url: "/beneficiaries",
      method: apiService.postWithAuth,
      body: body,
      onSuccess: (data) {
        return ApiResponse(success: true, message: data['message']);
      },
    );
  }

  Future<ApiResponse> verifyBankAcct({
    required int currencyId,
    required String bankId, //use the slug from bank lists (eg gt_bank)
    required String accountNum,
    String? amount,
  }) async {
    return await performApiCall(
      url: "//wallets/name-enquiry/$currencyId",
      method: apiService.postWithAuth,
      busyObjectName: verifyingBankState,
      body: {
        "destination_bank_uuid": bankId,
        "destination_bank_account_number": accountNum,
        "amount": amount,
      },
      onSuccess: (data) {
        printty(data["data"], level: 'verify bank account');
        _verifiedBank = verifyBankFromJson(json.encode(data["data"]));
        if (_verifiedBank != null) {
          accountNameC.text = _verifiedBank!.accountName!;
        }
        return apiResponse;
      },
    );
  }

  Future<ApiResponse> verifyMobileWalletByCurrecyId({
    required int currencyId,
    required String destinationNumber,
  }) async {
    return await performApiCall(
      url: "/wallets/verify-mobile-wallet/$currencyId",
      method: apiService.postWithAuth,
      body: {
        "destination_number": destinationNumber,
      },
      busyObjectName: verifyingBankState,
      onSuccess: (data) {
        return ApiResponse(
            success: true, data: apiResponse.data["data"]?["status"]);
      },
    );
  }

  clearData() {
    bankNameC.clear();
    accountNumC.clear();
    accountNameC.clear();

    fNameC.clear();
    lNameC.clear();
    emailC.clear();
  }

  @override
  void dispose() {
    printty("Beneficiary Dispose called");
    bankNameC.dispose();
    accountNameC.dispose();
    accountNumC.dispose();

    fNameC.dispose();
    lNameC.dispose();
    emailC.dispose();

    super.dispose();
  }
}
