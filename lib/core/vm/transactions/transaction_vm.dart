import 'dart:convert';

import 'package:collection/collection.dart';
import 'package:intl/intl.dart';
import 'package:korrency/core/core.dart';

class TransactionState {
  static const String all = "all";
  static const String paginated = "paginated";
}

class TransactionVM extends BaseVM {
  TextEditingController startDateC = TextEditingController();
  TextEditingController startDateFormattedC = TextEditingController();
  TextEditingController endDateC = TextEditingController();
  TextEditingController endDateFormattedC = TextEditingController();

  List<SendMoneyPurpose> _transactionReasons = [];
  List<SendMoneyPurpose> get transactionReasons => _transactionReasons;
  SendMoneyPurpose? _selectedReason;
  SendMoneyPurpose? get selectedReason => _selectedReason;

  List<String?> _statusFilter = [];
  List<String?> get statusFilter => _statusFilter;
  Currency? _selectedCurrency;
  Currency? get selectedCurrency => _selectedCurrency;
  bool _isQuerying = false;
  bool get isQuerying => _isQuerying;
  bool _gettingMore = false;
  bool get gettingMore => _gettingMore;
  bool _hasMore = true;
  bool get hasMore => _hasMore;
  int _currentPage = 1;
  int get currentPage => _currentPage;

  bool get dateBtnIsActive =>
      startDateC.text.isNotEmpty && endDateC.text.isNotEmpty;

  bool _showReasonWarning = false;
  bool get showReasonWarning => _showReasonWarning;
  setShowReasonWarning(bool value) {
    _showReasonWarning = value;
    reBuildUI();
  }

  setSelectedReason(SendMoneyPurpose? value) {
    _selectedReason = value;
    _showReasonWarning = false;
    reBuildUI();
  }

  setGettingMore(bool value) {
    _gettingMore = value;
    reBuildUI();
  }

  setStatusFilter({String? value, bool clear = false}) {
    if (clear) {
      _statusFilter.clear();
    }

    if (value != null) {
      if (_statusFilter.contains(value)) {
        _statusFilter.remove(value);
      } else {
        _statusFilter.add(value);
      }
    }

    printty(_statusFilter, level: "setStatusFilter");
    reBuildUI();
  }

  setSelectedCurrency(Currency? value) {
    _selectedCurrency = value;
    reBuildUI();
  }

  clearDates() {
    startDateC.clear();
    startDateFormattedC.clear();
    endDateC.clear();
    endDateFormattedC.clear();
  }

  int transactionPage = 1;
  int totalRecords = 0;
  List<Transaction> _recentTransactions = [];
  List<Transaction> get recentTransactions => _recentTransactions;
  List<Transaction> _transactionByCurrencyId = [];
  List<Transaction> get transactionByCurrencyId => _transactionByCurrencyId;

  // Group transactions by the date they were created
  Map<String, List<Transaction>> _store = {};
  Map<String, List<Transaction>> _groupedTransactions = {};
  Map<String, List<Transaction>> get groupedTransactions =>
      _groupedTransactions;

  searchTransaction(String query) {
    if (query.isEmpty) {
      _groupedTransactions = _store;
      reBuildUI();
      return;
    }
    printty(query);

    // Sort _groupedTransactions by description
    var sortedTransactions = _store.values
        .expand((element) => element)
        .where((element) =>
            element.description!.toLowerCase().contains(query.toLowerCase()))
        .toList();

    _groupedTransactions = groupBy(
      sortedTransactions,
      (Transaction transaction) =>
          DateFormat('dd MMM yyyy').format(transaction.createdAt!),
    );

    reBuildUI();
  }

  Future<ApiResponse> getTransactions() async {
    _gettingMore = false;
    _isQuerying = false;
    _currentPage = 1;
    return await performApiCall(
      url: "/transactions?page=1&limit=15",
      busyObjectName: TransactionState.all,
      method: apiService.getWithAuth,
      onSuccess: (data) {
        var transactions =
            transactionFromJson(json.encode(data["datatable"]["data"]));
        _hasMore = transactions.length == 15;
        // Group transactions by the date they were created
        var result = groupBy(
          transactions,
          (Transaction transaction) =>
              DateFormat('dd MMM yyyy').format(transaction.createdAt!),
        );
        _groupedTransactions = result;
        _store = result;

        // printty(_groupedTransactions, logLevel: "_groupedTransactions");
        _recentTransactions = transactions.take(3).toList();

        return ApiResponse(success: true, data: transactions);
      },
    );
  }

  Future<ApiResponse> getFilteredTransactions({
    int? currencyId,
    String? startDate,
    String? endDate,
    List<String?>? status,

    /// to diff wallet/currency transactions
    bool isAllTransaction = true,
  }) async {
    _isQuerying = true;
    // Initialize UriBuilder
    UriBuilder uriBuilder = UriBuilder("/transactions")
      ..addQueryParameterIfNotEmpty("page", transactionPage.toString())
      ..addQueryParameterIfNotEmpty("limit", "15")
      ..addQueryParameterIfNotEmpty("currency_id", currencyId?.toString() ?? "")
      ..addQueryParameterIfNotEmpty("start_date", startDate ?? "")
      ..addQueryParameterIfNotEmpty("end_date", endDate ?? "");

    if (status != null) {
      for (var stat in status) {
        if (stat != null) {
          uriBuilder.addQueryParameter("status[]", stat);
        }
      }
    }

    return await performApiCall(
      url: uriBuilder.build().toString(),
      busyObjectName: TransactionState.all,
      method: apiService.getWithAuth,
      onSuccess: (data) {
        var transactions =
            transactionFromJson(json.encode(data["datatable"]["data"]));

        if (isAllTransaction) {
          // Group transactions by the date they were created
          var result = groupBy(
            transactions,
            (Transaction transaction) =>
                DateFormat('dd MMM yyyy').format(transaction.createdAt!),
          );

          _groupedTransactions = result;
          _store = result;

          // printty(_groupedTransactions, logLevel: "getFilteredTransactions");
          _recentTransactions = transactions.take(2).toList();
        } else {
          //  Wallet/currency transactions
          _transactionByCurrencyId = transactions;
        }

        return ApiResponse(success: true, data: transactions);
      },
    );
  }

  Future<ApiResponse> getPaginatedTransactions() async {
    _currentPage++;

    setGettingMore(true);

    return await performApiCall(
      url: "/transactions?page=$_currentPage&limit=15",
      busyObjectName: TransactionState.paginated,
      method: apiService.getWithAuth,
      onSuccess: (data) {
        var transactions =
            transactionFromJson(json.encode(data["datatable"]["data"]));
        _hasMore = transactions.length == 15;
        // Group transactions by the date they were created
        printty('Has more: $_hasMore, Transactions: $transactions');
        var result = groupBy(
          transactions,
          (Transaction transaction) =>
              DateFormat('dd MMM yyyy').format(transaction.createdAt!),
        );
        _groupedTransactions.addAll(result);
        _store.addAll(result);

        setGettingMore(false);
        return ApiResponse(success: true, data: transactions);
      },
    );
  }

  Future<ApiResponse> getTransactionPurposes() async {
    return await performApiCall(
      url: "/transactions/purposes",
      method: apiService.getWithAuth,
      onSuccess: (data) {
        _transactionReasons =
            sendMoneyReasonFromJson(json.encode(data["data"]));
        return apiResponse;
      },
    );
  }

  void resetData() {
    printty("Resetting Transaction VM data ");

    _recentTransactions = [];
    _groupedTransactions = {};
    _store = {};
    _statusFilter = [];
    _selectedCurrency = null;
    clearDates();
  }

  @override
  void dispose() {
    printty("TransactionVM disposed");

    startDateC.dispose();
    endDateC.dispose();
    startDateFormattedC.dispose();
    endDateFormattedC.dispose();

    super.dispose();
  }
}
