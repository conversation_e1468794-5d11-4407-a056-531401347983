import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';

const String images = "assets/images";

class AppImages {
  static const logo = "$images/logo.png";
  static const noimage = "$images/noimage.jpg";
  static const iconLogo = "$images/iconLogo.png";
  static const logoText = "$images/logoText.png";
  static const update = "$images/update.png";
  static const splashBg = "$images/splashBg.png";
  static const smGradient = "$images/smGradient.png";

  static const fingerPrint = "$images/fingerPrint.png";
  static const fingerprint1 = "$images/fingerprint1.png";
  static const faceId = "$images/faceId.png";

  static const circleCheck = "$images/circleCheck.png";
  static const circleError = "$images/circleError.png";
  static const success = "$images/success.png";
  static const failed = "$images/failed.png";

  static const ngnFlag = "$images/ngn.png";
  static const cadFlag = "$images/cad.png";
  static const identity = "$images/identity.png";

  static const phoneUser = "$images/phoneUser.png";
  static const user = "$images/user.png";
  static const team = "$images/team.png";
  static const welcome = "$images/welcome.png";
  static const welcomeBg = "$images/welcomeBg.png";
  static const rateOffer = "$images/rateOffer.png";

  // Dashboard
  static const fab = "$images/dashboard/fab.png";
  static const avater = "$images/dashboard/avater.png";
  static const pattern = "$images/dashboard/pattern.png";

  // Emojis
  static const emoji1 = "$images/emojis/emoji1.png";
  static const emoji2 = "$images/emojis/emoji2.png";
  static const emoji3 = "$images/emojis/emoji3.png";
  static const emoji4 = "$images/emojis/emoji4.png";
  static const emoji5 = "$images/emojis/emoji5.png";

  // Cards
  static const cad = "$images/cards/cad.png";
  static const ngn = "$images/cards/ngn.png";
  static const ghs = "$images/cards/ghs.png";
  static const kes = "$images/cards/kes.png";
  static const usd = "$images/cards/usd.png";
  static const other = "$images/cards/other.png";

  // Onboard
  static const onboard0 = "$images/onboard/onboard0.png";
  static const onboard1 = "$images/onboard/onboard1.png";
  static const onboard2 = "$images/onboard/onboard2.png";
  static const onboard3 = "$images/onboard/onboard3.png";

  // Action
  static const convertAction = "$images/action/convert.png";
  static const earningsAction = "$images/action/earnings.png";
  static const inviteAction = "$images/action/invite.png";
  static const limitAction = "$images/action/limit.png";
  static const ratesAction = "$images/action/rate.png";

  static const gtb = "$images/gtb.png";
  static const transaction = "$images/transaction.png";
  static const contactUs = "$images/contactUs.png";
  static const warning = "$images/warning.png";
  static const trustedDevice = "$images/trustedDevice.png";

  static const howitworks = "$images/howitworks.png";
  static const p2p = "$images/p2p.png";
  static const trash = "$images/trash.png";
  static const rateAlert = "$images/rateAlert.png";
  static const interac = "$images/interac.png";
  static const envelope = "$images/envelope.png";
  static const interacK = "$images/inerac-k.png";
  static const edu = "$images/edu.jpg";
  static const occupation = "$images/occupation.png";
  static const newOffer = "$images/newOffer.png";

  static const star = "$images/star.png";
  static const ratingHeart = "$images/ratingHeart.png";
  static const ratingCancel = "$images/ratingCancel.png";
  static const profileBadge = "$images/profileBadge.png";
  static const kycWelcome = "$images/kycWelcome.png";
  static const r2 = "$images/r2.png";

  static const fundWallet = "$images/fundWallet.png";
  static const interacBg = "$images/interacBg.png";
  static const locKey = "$images/locKey.png";
  static const referrals = "$images/referrals.png";
  static const lTrash = "$images/lTrash.png";
}

// Image Helper
SizedBox imageHelper(String image,
    {double? height, double? width, BoxFit? fit}) {
  return SizedBox(
    height: height,
    width: width,
    child: Image.asset(
      image,
      fit: fit,
    ),
  );
}

Widget cacheNetWorkImage(
  String url, {
  double? height,
  double? width,
  BoxFit? fit,
}) {
  return CachedNetworkImage(
    imageUrl: url,
    width: width,
    height: height,
    fit: fit,
    fadeInDuration: const Duration(milliseconds: 2000),
    placeholder: (context, url) {
      return const SizedBox();
    },
    errorWidget: (context, url, error) {
      return const SizedBox();
    },
  );
}
