import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:korrency/core/extensions/strings_ext.dart';

class AppUtils {
  static String nairaSymbolWithSpace = "₦";
  static String dollarSymbolWithSpace = "\$";
  static String kCadFlag =
      "https://korrency-icons.s3.us-east-1.amazonaws.com/flags/CAD.svg";
  static String kInteracPng =
      "https://korrency-banks.s3.us-east-1.amazonaws.com/interac.png";

  static String korrencyCreateTicket =
      "https://korrency.freshdesk.com/support/tickets/new";
  static String korrency = "https://korrency.com";
  static String korrencyAbout = "https://korrency.com/about";
  static String korrencyBlog = "https://korrency.com/blog";
  static String korrencyPolicy = "https://korrency.com/privacy-policy";
  static String korrencyTermsAndCondition =
      "https://korrency.com/terms-and-condition";
  static String referralTermsAndCondition =
      "https://korrency.com/referral-program-terms-and-condition";
  static String korrencyFAQ = "https://korrency.com/faq";
  static String korrencyDepositEmail = "<EMAIL>";

  static int jobIdSelfieAndDocument = 5;
  static int jobIdSelfie = 4;
  static int jobIdDocument = 6;
  static int jobIdBiometric = 1;

  static String maskEmail(String input, {int consoreLevel = 2}) {
    // Check if input is an email
    final parts = input.split("@");

    if (parts.length == 2) {
      // Handle email masking
      final emailcaracter = input.replaceRange(
          consoreLevel, parts[0].length, "*" * (parts[0].length - 2));
      return emailcaracter;
    } else {
      // Handle account number masking
      if (input.length <= 4) {
        return input; // Don't mask if too short
      }

      // Show first 2 and last 2 characters, mask the middle
      final firstPart = input.substring(0, 2);
      final lastPart = input.substring(input.length - 2);
      final middleMask = "*" * (input.length - 4);

      return firstPart + middleMask + lastPart;
    }
  }

  static String dateFormatter(String date) {
    var formatter = DateFormat('yyyy-MM-dd');
    DateTime dateTime = formatter.parse(date);
    String formatted = formatter.format(dateTime);
    var reformatDate = formatted.split("-");
    String formattedDate =
        "${reformatDate[2]} + '-' + ${reformatDate[1]} + '-' + ${reformatDate[0]}";

    //print(formattedDate);
    return formattedDate;
  }

  static String dateFormatter2(DateTime date) {
    var formatter = DateFormat('dd MMM yyyy');

    String formatted = formatter.format(date);

    //print(formattedDate);
    return formatted;
  }

  //e.g 23rd March, 2021
  static String dayWithSuffixMonthAndYear(DateTime date) {
    var suffix = "th";
    //var suffix = "";
    var digit = date.day % 10;
    if ((digit > 0 && digit < 4) && (date.day < 11 || date.day > 13)) {
      suffix = ["st", "nd", "rd"][digit - 1];
    }
    return DateFormat("d'$suffix' MMM, y").format(date);
  }

  //e.g 23rd March, 2021 4:40PM
  static String d(DateTime date) {
    var suffix = "th";
    var digit = date.day % 10;
    if ((digit > 0 && digit < 4) && (date.day < 11 || date.day > 13)) {
      suffix = ["st", "nd", "rd"][digit - 1];
    }
    String dd = DateFormat("d'$suffix' MMM, y").format(date);
    String tt = DateFormat.jm().format(date);

    return "$dd $tt";
  }

  static String expireAtFormatDuration(Duration duration) {
    if (duration.inMinutes <= 0) {
      return "Expired";
    }

    String twoDigits(int n) => n.toString().padLeft(2, "0");
    String twoDigitHours = twoDigits(duration.inHours.remainder(60));
    String twoDigitMinutes = twoDigits(duration.inMinutes.remainder(60));
    String twoDigitSeconds = twoDigits(duration.inSeconds.remainder(60));

    return "Expires in $twoDigitHours:$twoDigitMinutes:$twoDigitSeconds";
  }

  // Output: 12th Jan, 2025 • 9:00am
  static String formatDateTimeTwo(DateTime dateTime) {
    // Create the ordinal suffix (1st, 2nd, 3rd, 4th, etc.)
    String getOrdinalSuffix(int day) {
      if (day >= 11 && day <= 13) {
        return 'th';
      }
      switch (day % 10) {
        case 1:
          return 'st';
        case 2:
          return 'nd';
        case 3:
          return 'rd';
        default:
          return 'th';
      }
    }

    String day = dateTime.day.toString();
    String ordinalSuffix = getOrdinalSuffix(dateTime.day);
    String month = DateFormat.MMM().format(dateTime); // Jan, Feb, etc.
    String year = dateTime.year.toString();
    String time = DateFormat.jm().format(dateTime); // 9:00 AM

    return '$day$ordinalSuffix $month, $year • ${time.toLowerCase()}';
  }

  static String formatDateTime(String dateTimeString) {
    // Parse the input date string
    DateTime dateTime = DateTime.parse(dateTimeString);

    // Format the date and time
    String formattedDate = DateFormat.yMMMd().format(dateTime);
    String formattedTime = DateFormat.Hm().format(dateTime);

    // Construct the final formatted string
    return '$formattedDate | $formattedTime';
  }

  ///format currency
  static String formatAmount({required double amount}) {
    //a final oCcy = NumberFormat("#,##0", "en_US");
    NumberFormat oCcy = NumberFormat.decimalPattern('en_us');
    String formattedAmount = oCcy.format(amount);
    return formattedAmount;
  }

  /// takes a string
  static String formatAmountString(String amount) {
    return formatAmount(amount: double.tryParse(amount) ?? 0.0);
  }

  /// takes a string
  static String formatAmountDoubleString(String amount) {
    return formatAmountWithDecimal(amount: double.tryParse(amount) ?? 0.0);
  }

  static String formatAmountWithDecimal({required double amount}) {
    final oCcy = NumberFormat("#,##0.00", "en_US");
    String formattedAmount = oCcy.format(amount);
    return formattedAmount;
  }

  static String formatPhoneNumber(String phoneNumber) {
    // Remove all non-digit characters
    String digits = phoneNumber.replaceAll(RegExp(r'[^\d]'), '');

    if (digits.length == 9) {
      // Format as (603) - 775 - 943
      return '(${digits.substring(0, 3)}) - ${digits.substring(3, 6)} - ${digits.substring(6)}';
    } else if (digits.length == 10) {
      // Format as (603) - 775 - 9434
      return '(${digits.substring(0, 3)}) - ${digits.substring(3, 6)} - ${digits.substring(6)}';
    } else {
      // Return original if it doesn't match expected lengths
      return phoneNumber;
    }
  }

  ///convert datetime format to Month, Day and Year
  static String dateAndTime({required DateTime date}) {
    return DateFormat.yMMMd().format(date);
  }

  static launchURL({required String url, LaunchMode? launchMode}) async {
    try {
      if (await canLaunchUrl(Uri.parse(url))) {
        return await launchUrl(Uri.parse(url),
            mode: launchMode ?? LaunchMode.inAppWebView);
      } else {
        return false;
      }
    } catch (e) {
      return false;
    }
  }

  static launchEmail({required String url}) async {
    final Uri emailLaunchUri = Uri(scheme: 'mailto', path: url);

    await launchUrl(emailLaunchUri);
  }

  static openPhoneDialler({required String url}) async {
    final Uri emailLaunchUri = Uri(scheme: 'tel', path: url);

    await launchUrl(emailLaunchUri);
  }

  static bool compareVersions(String version1, String version2) {
    List<int> v1Parts = version1.split('.').map(int.parse).toList();
    List<int> v2Parts = version2.split('.').map(int.parse).toList();

    for (int i = 0; i < v1Parts.length && i < v2Parts.length; i++) {
      if (v1Parts[i] > v2Parts[i]) return true;
      if (v1Parts[i] < v2Parts[i]) return false;
    }

    // If we've reached here, check the length
    return v1Parts.length > v2Parts.length;
  }

  /// Generate initials from beneficiary name
  /// For multiple words: take first letter of each word and capitalize
  /// For single word: take first and second letter and capitalize
  static String getInitials(String name) {
    if (name.isEmpty) return '';

    final words = name.trim().split(RegExp(r'\s+'));

    if (words.length == 1) {
      // Single word: take first and second letter if available
      final word = words[0];
      if (word.length == 1) {
        return word.toUpperCase();
      } else if (word.length >= 2) {
        return word.substring(0, 2).toUpperCase();
      } else {
        return word.toUpperCase();
      }
    } else {
      // Multiple words: take first letter of each word
      return words
          .map((word) => word.isNotEmpty ? word[0].toUpperCase() : '')
          .take(2)
          .join('');
    }
  }

  /// Pick One name from full name
  static String pickName(String fullName) {
    if (fullName.trim().isEmpty) return "";

    // Split by spaces and remove empty parts
    final parts = fullName.trim().split(RegExp(r"\s+"));

    String result = "";
    if (parts.isNotEmpty && parts.first.isNotEmpty) {
      result = parts.first; // First name
    } else if (parts.isNotEmpty && parts.last.isNotEmpty) {
      result = parts.last; // Last name as fallback
    }

    // Capitalize first letter
    if (result.isNotEmpty) {
      result = result[0].toUpperCase() + result.substring(1).toLowerCase();
    }

    return result;
  }

  /// Format date with relative time (Today, Yesterday, or actual date)
  /// Returns format like "Today at 12:20 pm", "Yesterday at 12:20 pm", or "Jan 15, 2024 at 12:20 pm"
  static String formatRelativeDateTime(String? dateTimeString) {
    if (dateTimeString == null || dateTimeString.isEmpty) {
      return '';
    }

    try {
      DateTime dateTime = DateTime.parse(dateTimeString);
      DateTime now = DateTime.now();
      DateTime today = DateTime(now.year, now.month, now.day);
      DateTime yesterday = today.subtract(const Duration(days: 1));
      DateTime inputDate = DateTime(dateTime.year, dateTime.month, dateTime.day);

      // Format time as "12:20 pm"
      String timeFormatted = DateFormat.jm().format(dateTime).toLowerCase();

      if (inputDate == today) {
        return "Today at $timeFormatted";
      } else if (inputDate == yesterday) {
        return "Yesterday at $timeFormatted";
      } else {
        // Format as "Jan 15, 2024 at 12:20 pm"
        String dateFormatted = DateFormat.yMMMd().format(dateTime);
        return "$dateFormatted at $timeFormatted";
      }
    } catch (e) {
      // If parsing fails, return the original string or empty
      return dateTimeString;
    }
  }

  /// Handles text capitalization for text fields with automatic cursor positioning
  /// This function capitalizes the input text and updates the controller if needed
  /// 
  /// Parameters:
  /// - [controller]: The TextEditingController to update
  /// - [newText]: The new text input from the user
  /// 
  /// Usage example:
  /// ```dart
  /// onChanged: (value) {
  ///   AppUtils.handleTextCapitalization(myController, value);
  ///   // Your other logic here
  /// }
  /// ```
  static void handleTextCapitalization(
    TextEditingController controller,
    String newText,
  ) {
    if (newText.isNotEmpty) {
      final capitalizedText = newText.capitalize();
      if (capitalizedText != newText) {
        controller.value = controller.value.copyWith(
          text: capitalizedText,
          selection: TextSelection.collapsed(offset: capitalizedText.length),
        );
      }
    }
  }
}
