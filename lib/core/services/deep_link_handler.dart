// ignore_for_file: use_build_context_synchronously

import 'package:korrency/core/core.dart';

enum DeepLinkType {
  referral,
  general,
  transaction,
  profile,
  unknown,
}

class DeepLinkData {
  final DeepLinkType type;
  final Map<String, dynamic> parameters;
  final String? referralCode;
  final String? targetRoute;
  final Map<String, dynamic>? routeArguments;

  const DeepLinkData({
    required this.type,
    required this.parameters,
    this.referralCode,
    this.targetRoute,
    this.routeArguments,
  });

  @override
  String toString() {
    return 'DeepLinkData(type: $type, referralCode: $referralCode, targetRoute: $targetRoute, parameters: $parameters)';
  }
}

class DeepLinkHandler {
  static DeepLinkHandler? _instance;
  static DeepLinkHandler get instance => _instance ??= DeepLinkHandler._();

  DeepLinkHandler._();

  // Track if app was opened via deep link
  bool _appOpenedViaDeepLink = false;
  String? _pendingDeepLinkPath;

  /// Set flag when app is opened via deep link
  void setAppOpenedViaDeepLink(String? path) {
    _appOpenedViaDeepLink = true;
    _pendingDeepLinkPath = path;
    printty('📋 App opened via deep link: $path');
  }

  /// Check if app was opened via deep link
  bool get wasOpenedViaDeepLink => _appOpenedViaDeepLink;

  /// Get pending deep link path
  String? get pendingDeepLinkPath => _pendingDeepLinkPath;

  /// Clear deep link flags
  void clearDeepLinkFlags() {
    _appOpenedViaDeepLink = false;
    _pendingDeepLinkPath = null;
  }

  /// Process raw deep link data and determine routing action
  DeepLinkData processDeepLink(Map<String, dynamic> clickEvent) {
    try {
      printty('🔍 Processing deep link click event: $clickEvent');

      // Extract basic information
      final String? link = clickEvent['link'];
      final String? deepLinkValue = clickEvent['deep_link_value'];
      final String? campaign = clickEvent['campaign'];
      final String? mediaSource = clickEvent['media_source'];

      printty('🔍 Link: $link');
      printty('🔍 Deep Link Value: $deepLinkValue');
      printty('🔍 Campaign: $campaign');
      printty('🔍 Media Source: $mediaSource');

      // Try to extract referral code
      String? referralCode = _extractReferralCode(clickEvent, link);

      // Determine deep link type and target route
      DeepLinkType type = _determineDeepLinkType(clickEvent, referralCode);
      String? targetRoute = _determineTargetRoute(type, clickEvent);
      Map<String, dynamic>? routeArguments =
          _buildRouteArguments(type, clickEvent, referralCode);

      final deepLinkData = DeepLinkData(
        type: type,
        parameters: clickEvent,
        referralCode: referralCode,
        targetRoute: targetRoute,
        routeArguments: routeArguments,
      );

      printty('✅ Processed deep link: $deepLinkData');
      return deepLinkData;
    } catch (e) {
      printty('❌ Error processing deep link: $e');
      return DeepLinkData(
        type: DeepLinkType.unknown,
        parameters: clickEvent,
      );
    }
  }

  /// Execute the routing action based on processed deep link data
  Future<bool> executeDeepLink(DeepLinkData deepLinkData) async {
    try {
      final context = NavigatorKeys.appNavigatorKey.currentContext;
      if (context == null) {
        printty('⚠️ No navigator context available for deep link execution');
        return false;
      }

      printty('🚀 Executing deep link: $deepLinkData');

      // Handle different types of deep links
      switch (deepLinkData.type) {
        case DeepLinkType.referral:
          return await _handleReferralDeepLink(context, deepLinkData);

        case DeepLinkType.general:
          return await _handleGeneralDeepLink(context, deepLinkData);

        case DeepLinkType.transaction:
          return await _handleTransactionDeepLink(context, deepLinkData);

        case DeepLinkType.profile:
          return await _handleProfileDeepLink(context, deepLinkData);

        case DeepLinkType.unknown:
        default:
          return await _handleUnknownDeepLink(context, deepLinkData);
      }
    } catch (e) {
      printty('❌ Error executing deep link: $e');
      return false;
    }
  }

  /// Extract referral code from various possible sources
  String? _extractReferralCode(Map<String, dynamic> clickEvent, String? link) {
    // Try direct fields first
    String? referralCode = clickEvent['referrer'] ??
        clickEvent['referral_code'] ??
        clickEvent['ref'] ??
        clickEvent['affiliate_id'];

    if (referralCode != null && referralCode.isNotEmpty) {
      printty('🎯 Found referral code in click event: $referralCode');
      return referralCode;
    }

    // Try to extract from link URL
    if (link != null) {
      try {
        final uri = Uri.parse(link);

        // Check query parameters
        referralCode = uri.queryParameters['referrer'] ??
            uri.queryParameters['referral_code'] ??
            uri.queryParameters['ref'] ??
            uri.queryParameters['affiliate_id'];

        if (referralCode != null && referralCode.isNotEmpty) {
          printty('🎯 Found referral code in URL query: $referralCode');
          return referralCode;
        }

        // Check path segments (e.g., /ref/ABC123)
        final pathSegments = uri.pathSegments;
        if (pathSegments.length >= 2) {
          for (int i = 0; i < pathSegments.length - 1; i++) {
            if (pathSegments[i] == 'ref' || pathSegments[i] == 'referral') {
              referralCode = pathSegments[i + 1];
              printty('🎯 Found referral code in URL path: $referralCode');
              return referralCode;
            }
          }
        }
      } catch (e) {
        printty('❌ Error parsing link for referral: $e');
      }
    }

    return null;
  }

  /// Determine the type of deep link
  DeepLinkType _determineDeepLinkType(
      Map<String, dynamic> clickEvent, String? referralCode) {
    // Check for referral
    if (referralCode != null && referralCode.isNotEmpty) {
      return DeepLinkType.referral;
    }

    // Check for specific campaign types
    final campaign = clickEvent['campaign']?.toString().toLowerCase();
    final link = clickEvent['link']?.toString().toLowerCase();

    if (campaign != null) {
      if (campaign.contains('transaction') || campaign.contains('transfer')) {
        return DeepLinkType.transaction;
      }
      if (campaign.contains('profile') || campaign.contains('account')) {
        return DeepLinkType.profile;
      }
    }

    if (link != null) {
      if (link.contains('/transaction') || link.contains('/transfer')) {
        return DeepLinkType.transaction;
      }
      if (link.contains('/profile') || link.contains('/account')) {
        return DeepLinkType.profile;
      }
    }

    // Default to general
    return DeepLinkType.general;
  }

  /// Determine target route based on deep link type
  String? _determineTargetRoute(
      DeepLinkType type, Map<String, dynamic> clickEvent) {
    switch (type) {
      case DeepLinkType.referral:
        return RoutePath.createAcctScreen;
      case DeepLinkType.transaction:
        return RoutePath.transactionScreen;
      case DeepLinkType.profile:
        return RoutePath.profileScreen;
      case DeepLinkType.general:
      case DeepLinkType.unknown:
      default:
        return RoutePath.dashboardNav;
    }
  }

  /// Build route arguments for navigation
  Map<String, dynamic>? _buildRouteArguments(DeepLinkType type,
      Map<String, dynamic> clickEvent, String? referralCode) {
    switch (type) {
      case DeepLinkType.referral:
        return {'referralCode': referralCode};
      case DeepLinkType.general:
        return {'index': 0}; // Home tab for dashboard
      default:
        return null;
    }
  }

  /// Handle referral deep links
  Future<bool> _handleReferralDeepLink(
      BuildContext context, DeepLinkData data) async {
    try {
      printty('🎯 Handling referral deep link with code: ${data.referralCode}');

      // Store referral code for signup process
      if (data.referralCode != null) {
        await StorageService.storeString(
            StorageKey.deepLinkValue, data.referralCode!);

        // Set in OnBoardVM if available
        try {
          context.read<OnBoardVM>().setDeepLinkReferralCode(data.referralCode!);
          printty('✅ Referral code set in OnBoardVM');
        } catch (e) {
          printty(
              '⚠️ OnBoardVM not available, referral stored for later use: $e');
        }
      }

      // Navigate based on user state
      final authUser = context.read<AuthUserVM>().user;

      if (authUser != null) {
        // User already logged in, go to dashboard
        Navigator.pushNamedAndRemoveUntil(
          context,
          RoutePath.dashboardNav,
          (route) => false,
          arguments: 0,
        );
      } else {
        // Navigate to signup
        Navigator.pushNamedAndRemoveUntil(
          context,
          RoutePath.splashScreen,
          (route) => false,
        );
      }

      // Log analytics event
      AppsFlyerService.instance.logEvent('referral_deep_link_opened', {
        'referral_code': data.referralCode,
        'timestamp': DateTime.now().toIso8601String(),
      });

      return true;
    } catch (e) {
      printty('❌ Error handling referral deep link: $e');
      return false;
    }
  }

  /// Handle general deep links
  Future<bool> _handleGeneralDeepLink(
      BuildContext context, DeepLinkData data) async {
    try {
      printty('🔗 Handling general deep link');

      // Navigate based on user state
      final authUser = context.read<AuthUserVM>().user;

      if (authUser != null) {
        // User logged in, go to dashboard
        Navigator.pushNamedAndRemoveUntil(
          context,
          RoutePath.dashboardNav,
          (route) => false,
          arguments: 0,
        );
      } else {
        // Navigate to intro
        Navigator.pushNamedAndRemoveUntil(
          context,
          RoutePath.splashScreen,
          (route) => false,
        );
      }

      // Log analytics event
      AppsFlyerService.instance.logEvent('general_deep_link_opened', {
        'campaign': data.parameters['campaign'],
        'media_source': data.parameters['media_source'],
        'timestamp': DateTime.now().toIso8601String(),
      });

      return true;
    } catch (e) {
      printty('❌ Error handling general deep link: $e');
      return false;
    }
  }

  /// Handle transaction-related deep links
  Future<bool> _handleTransactionDeepLink(
      BuildContext context, DeepLinkData data) async {
    try {
      printty('💰 Handling transaction deep link');

      // Navigate based on user state
      final authUser = context.read<AuthUserVM>().user;

      if (authUser != null) {
        // User logged in, go to transaction screen
        Navigator.pushNamedAndRemoveUntil(
          context,
          RoutePath.transactionScreen,
          (route) => false,
        );
      } else {
        // User not logged in, go to login first
        Navigator.pushNamedAndRemoveUntil(
          context,
          RoutePath.loginScreen,
          (route) => false,
        );
      }

      return true;
    } catch (e) {
      printty('❌ Error handling transaction deep link: $e');
      return false;
    }
  }

  /// Handle profile-related deep links
  Future<bool> _handleProfileDeepLink(
      BuildContext context, DeepLinkData data) async {
    try {
      printty('👤 Handling profile deep link');

      // Navigate based on user state
      final authUser = context.read<AuthUserVM>().user;

      if (authUser != null) {
        // User logged in, go to profile screen
        Navigator.pushNamedAndRemoveUntil(
          context,
          RoutePath.profileScreen,
          (route) => false,
        );
      } else {
        // User not logged in, go to login first
        Navigator.pushNamedAndRemoveUntil(
          context,
          RoutePath.loginScreen,
          (route) => false,
        );
      }

      return true;
    } catch (e) {
      printty('❌ Error handling profile deep link: $e');
      return false;
    }
  }

  /// Handle unknown deep links
  Future<bool> _handleUnknownDeepLink(
      BuildContext context, DeepLinkData data) async {
    try {
      printty('❓ Handling unknown deep link, defaulting to appropriate screen');

      // Navigate based on user state
      final authUser = context.read<AuthUserVM>().user;

      if (authUser != null) {
        // User logged in, go to dashboard
        Navigator.pushNamedAndRemoveUntil(
          context,
          RoutePath.dashboardNav,
          (route) => false,
          arguments: 0,
        );
      } else {
        // Navigate to intro
        Navigator.pushNamedAndRemoveUntil(
          context,
          RoutePath.splashScreen,
          (route) => false,
        );
      }

      // Log analytics event for unknown deep links
      AppsFlyerService.instance.logEvent('unknown_deep_link_opened', {
        'raw_data': data.parameters.toString(),
        'timestamp': DateTime.now().toIso8601String(),
      });

      return true;
    } catch (e) {
      printty('❌ Error handling unknown deep link: $e');
      return false;
    }
  }

  /// Process deep link from URL string (for testing or other sources)
  Future<bool> processDeepLinkFromUrl(String url) async {
    try {
      printty('🔗 Processing deep link from URL: $url');

      final uri = Uri.parse(url);
      final clickEvent = {
        'link': url,
        'referrer': uri.queryParameters['referrer'],
        'referral_code': uri.queryParameters['referral_code'],
        'ref': uri.queryParameters['ref'],
        'campaign': uri.queryParameters['campaign'],
        'media_source': uri.queryParameters['media_source'],
      };

      // Remove null values
      clickEvent.removeWhere((key, value) => value == null);

      final deepLinkData = processDeepLink(clickEvent);
      return await executeDeepLink(deepLinkData);
    } catch (e) {
      printty('❌ Error processing deep link from URL: $e');
      return false;
    }
  }
}
