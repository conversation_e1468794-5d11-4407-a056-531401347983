// ignore_for_file: use_build_context_synchronously

import 'package:flutter/services.dart';
import 'package:korrency/session_manager.dart';
import 'package:korrency/ui/screens/splash/splash.dart';
import 'package:local_session_timeout/local_session_timeout.dart';

import 'core/core.dart';

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _setupAppsFlyerListeners();
    });
  }

  void _setupAppsFlyerListeners() {
    // Listen to conversion data
    AppsFlyerService.instance.conversionDataStream?.listen((data) {
      printty("conversionData: $data");
    });

    // // Listen to deep link data - now handled by DeepLinkHandler
    AppsFlyerService.instance.deepLinkStream?.listen((data) {
      printty("deepLink received in MyApp: $data");

      // The actual deep link processing is now handled by the
      // DeepLinkHandler through the AppsFlyer service callbacks.
      // This listener is kept for logging and monitoring purposes.

      // Log the deep link event for debugging
      printty("Deep link data keys: ${data.keys.toList()}");

      // If there's any additional app-level processing needed,
      // it can be added here. For now, the DeepLinkHandler
      // manages all routing and referral code processing.
    });
  }

  @override
  void dispose() {
    AppsFlyerService.instance.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark,
      ),
    );

    final sessionConfig = SessionConfig(
        invalidateSessionForAppLostFocus:
            Duration(minutes: EnvConfig.focusTimeout),
        invalidateSessionForUserInactivity:
            Duration(minutes: EnvConfig.inactivityTimeout));

    sessionConfig.stream.listen((SessionTimeoutState timeoutEvent) {
      sessionStateStream.add(SessionState.stopListening);
      // if (context.watch<AuthUserVM>().user != null) {}
      if (timeoutEvent == SessionTimeoutState.userInactivityTimeout) {
        Navigator.pushNamed(NavigatorKeys.appNavigatorKey.currentContext!,
            RoutePath.welcomeBackScreen,
            arguments: false);
      } else if (timeoutEvent == SessionTimeoutState.appFocusTimeout) {
        Navigator.pushNamed(NavigatorKeys.appNavigatorKey.currentContext!,
            RoutePath.welcomeBackScreen,
            arguments: false);
      }
    });
    return MultiProvider(
      providers: allProviders,
      child: ScreenUtilInit(
        designSize: const Size(390, 844),
        builder: (context, child) {
          return SessionTimeoutManager(
            sessionConfig: sessionConfig,
            sessionStateStream: sessionStateStream.stream,
            child: MaterialApp(
              title: 'Korrency',
              debugShowCheckedModeBanner: false,
              theme: ThemeData(
                splashColor: Colors.transparent,
                highlightColor: Colors.transparent,
                colorScheme: ColorScheme.fromSeed(seedColor: Colors.deepPurple),
                scaffoldBackgroundColor: AppColors.bgWhite,
                fontFamily: AppFont.outfit.family,
                useMaterial3: true,
              ),
              navigatorKey: NavigatorKeys.appNavigatorKey,
              home: SplashScreen(),
              // Prevent automatic routing to unknown routes
              initialRoute: RoutePath.splashScreen,
              // Use our custom route generator that handles deep link interception
              onGenerateRoute: AppRouters.getRoute,
              // Handle unknown routes gracefully
              onUnknownRoute: (RouteSettings settings) {
                printty('⚠️ Unknown route detected: ${settings.name}');
                // Always redirect unknown routes to splash screen
                // Deep links will be handled by AppsFlyer callbacks
                return AppRouters.getRoute(RouteSettings(
                  name: RoutePath.splashScreen,
                  arguments: settings.arguments,
                ));
              },
            ),
          );
        },
      ),
    );
  }
}
