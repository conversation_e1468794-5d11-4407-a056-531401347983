import 'dart:ui';

import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:korrency/core/core.dart';

class BusyOverlay extends StatefulWidget {
  final Widget child;
  final bool show;
  final Color? bgColor;

  const BusyOverlay(
      {super.key, required this.child, this.bgColor, this.show = false});

  @override
  State<BusyOverlay> createState() => _BusyOverlayState();
}

class _BusyOverlayState extends State<BusyOverlay> {
  @override
  Widget build(BuildContext context) {
    return Material(
      color: AppColors.transparent,
      child: SizedBox(
        width: Sizer.screenWidth,
        height: Sizer.screenWidth,
        child: Stack(
          children: <Widget>[
            widget.child,
            Visibility(
              visible: widget.show,
              child: BackdropFilter(
                filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
                child: Container(
                  color: Colors.black12.withValues(alpha: 0.1),
                ),
              ),
            ),
            Center(
              child: IgnorePointer(
                ignoring: !widget.show,
                child: Visibility(
                  visible: widget.show,
                  child: <PERSON><PERSON>it<PERSON>oader(),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// class LogoLoader extends StatefulWidget {
//   const LogoLoader({
//     super.key,
//     this.h,
//     this.w,
//   });

//   final double? h;
//   final double? w;

//   @override
//   State<LogoLoader> createState() => _LogoLoaderState();
// }

// class _LogoLoaderState extends State<LogoLoader>
//     with SingleTickerProviderStateMixin {
//   late final AnimationController _controller = AnimationController(
//     vsync: this,
//     duration: const Duration(milliseconds: 1000),
//   )..repeat();

//   @override
//   void dispose() {
//     _controller.dispose();
//     super.dispose();
//   }

//   @override
//   Widget build(BuildContext context) {
//     return AnimatedBuilder(
//       animation: _controller,
//       builder: (BuildContext context, Widget? child) {
//         return Transform.rotate(
//           angle: _controller.value *
//               2 *
//               3.141592653589793, // Multiply by 2π for a full rotation
//           child: child,
//         );
//       },
//       child: svgHelper(
//         AppSvgs.logo,
//         height: Sizer.height(50),
//         width: Sizer.width(50),
//       ),

//       // imageHelper(
//       //   AppImages.iconLogo,
//       //   height: Sizer.height(widget.h ?? 38),
//       //   width: Sizer.width(widget.w ?? 38),
//       // ),
//     );
//   }
// }

class SpinKitLoader extends StatelessWidget {
  const SpinKitLoader({super.key, this.size, this.color});
  final double? size;
  final Color? color;

  @override
  Widget build(BuildContext context) {
    return Center(
      child: SpinKitSpinningCircle(
        size: size ?? 50, //200
        itemBuilder: (BuildContext context, int i) {
          return svgHelper(
            AppSvgs.appIcons,
            color: color,
          );
        },
      ),
    );
  }
}
