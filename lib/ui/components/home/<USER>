import 'package:korrency/core/core.dart';

class HomeTab extends StatelessWidget {
  const HomeTab({
    super.key,
    this.isActive = false,
    required this.text,
    this.onTap,
  });

  final bool isActive;
  final String text;
  final Function()? onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: Sizer.width(10),
        ),
        height: Sizer.height(44),
        decoration: BoxDecoration(
          color: isActive ? AppColors.primaryBlue : AppColors.blueFD,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Center(
          child: Text(
            text,
            style: AppTypography.text15.medium.copyWith(
              color: isActive ? AppColors.white : AppColors.primaryBlue,
            ),
          ),
        ),
      ),
    );
  }
}
