import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class NewCustomerOfferModal extends StatefulWidget {
  const NewCustomerOfferModal({super.key});

  @override
  State<NewCustomerOfferModal> createState() => _NewCustomerOfferModalState();
}

class _NewCustomerOfferModalState extends State<NewCustomerOfferModal>
    with TickerProviderStateMixin {
  late AnimationController _slideController;
  late AnimationController _imageController;
  late AnimationController _fadeController;

  late Animation<Offset> _slideAnimation;
  late Animation<double> _imageAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();

    // Initialize animation controllers
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _imageController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    // Initialize animations
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutBack,
    ));

    _imageAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _imageController,
      curve: Curves.elasticOut,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeIn,
    ));

    // Start animations with staggered timing
    _startAnimations();
  }

  void _startAnimations() async {
    _slideController.forward();
    await Future.delayed(const Duration(milliseconds: 200));
    _imageController.forward();
    await Future.delayed(const Duration(milliseconds: 300));
    _fadeController.forward();
  }

  @override
  void dispose() {
    _slideController.dispose();
    _imageController.dispose();
    _fadeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<ConfigVM>(builder: (context, vm, _) {
      return Stack(
        clipBehavior: Clip.none,
        children: [
          SlideTransition(
            position: _slideAnimation,
            child: Container(
              margin: EdgeInsets.all(Sizer.radius(12)),
              padding: EdgeInsets.symmetric(horizontal: Sizer.width(24)),
              decoration: BoxDecoration(
                color: AppColors.white,
                borderRadius: BorderRadius.circular(Sizer.radius(12)),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const YBox(80),
                  FadeTransition(
                    opacity: _fadeAnimation,
                    child: Text(
                      'New Customer Offer',
                      style: AppTypography.text22.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                  const YBox(4),
                  FadeTransition(
                    opacity: _fadeAnimation,
                    child: Text(
                      "Send ${vm.newCustomersRateMinimumAmount} CAD or more on your first transfer and unlock a special rate just for you",
                      textAlign: TextAlign.center,
                      style: AppTypography.text14.copyWith(
                        color: AppColors.gray93,
                      ),
                    ),
                  ),
                  const YBox(32),
                  FadeTransition(
                    opacity: _fadeAnimation,
                    child: CustomBtn.solid(
                      borderRadius: BorderRadius.circular(Sizer.radius(20)),
                      isLoading: vm.isBusy,
                      onTap: () {
                        Navigator.pop(context);
                      },
                      text: "Ok, got it!",
                    ),
                  ),
                  const YBox(30),
                ],
              ),
            ),
          ),
          Positioned(
            top: -60,
            left: 0,
            right: 0,
            child: ScaleTransition(
              scale: _imageAnimation,
              child: Image.asset(
                AppImages.rateOffer,
                height: Sizer.height(130),
              ),
            ),
          )
        ],
      );
    });
  }
}
