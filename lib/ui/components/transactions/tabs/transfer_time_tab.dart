import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class TransferTimeTab extends StatelessWidget {
  const TransferTimeTab({
    super.key,
    required this.status,
    required this.recipient,
    this.initiatedAt,
    this.completedAt,
  });

  final String status;
  final String recipient;
  final String? initiatedAt;
  final String? completedAt;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(
        top: Sizer.height(30),
      ),
      padding: EdgeInsets.symmetric(
        horizontal: Sizer.width(12),
        vertical: Sizer.height(16),
      ),
      decoration: BoxDecoration(
        color: AppColors.bgWhite,
        borderRadius: BorderRadius.circular(Sizer.radius(12)),
        border: Border.all(color: AppColors.grayAEC),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        mainAxisSize: MainAxisSize.min,
        children: [
          TransferTimeLine(
            timelineHeight: 60,
            title: "Transfer Submitted",
            textOne:
                "We have received your transfer request. \n${initiatedAt ?? ''}",
          ),
          TransferTimeLine(
            timelineHeight: 60,
            title: "Transfer in Progress",
            textOne:
                "Your transfer is on its way to $recipient. \n${initiatedAt ?? ''}",
            boldWords: [recipient],
          ),
          TransferTimeLine(
            showTextContainerBg: status == TransferStatus.failed,
            dotIocn: switch (status) {
              TransferStatus.pending => AppSvgs.dotOutline,
              TransferStatus.failed => AppSvgs.failedDot,
              _ => AppSvgs.dot,
            },
            timelineHeight: 60,
            title: status == TransferStatus.failed
                ? "Payment Failed"
                : "Payment Completed",
            textOne: status == TransferStatus.failed
                ? "Your transfer to $recipient failed \nPlease try again. \n${completedAt ?? ''}"
                : "$recipient should receive the money in a few minutes. We'll notify you as soon as it's delivered \n${completedAt ?? ''}",
            boldWords: [recipient],
            isEnd: true,
          ),
        ],
      ),
    );
  }
}
