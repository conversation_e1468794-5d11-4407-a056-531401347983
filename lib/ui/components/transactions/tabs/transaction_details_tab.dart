import 'package:flutter/services.dart';
import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class TransactionDetailsTab extends StatefulWidget {
  const TransactionDetailsTab({
    super.key,
    required this.transaction,
  });

  final Transaction transaction;

  @override
  State<TransactionDetailsTab> createState() => _TransactionDetailsTabState();
}

class _TransactionDetailsTabState extends State<TransactionDetailsTab> {
  bool hideExchangeDetails = false;

  bool get sameCurrencyTransfer => widget.transaction.convertedCurrency == null;
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // if (transaction.beneficiary != null)
        //   TransactionDetailsListTile(
        //     leftText: 'Beneficiary name',
        //     rightText: transaction
        //             .beneficiary?.accountName ??
        //         '',
        //   ),
        if (widget.transaction.beneficiary != null)
          TransactionDetailsListTile(
            leftText: 'Bank',
            rightText: widget.transaction.beneficiary?.institutionName ?? '',
          ),
        if (widget.transaction.beneficiary != null)
          TransactionDetailsListTile(
            leftText: 'Account #',
            rightText: widget.transaction.beneficiary?.accountIdentifier ?? '',
          ),
        TransactionDetailsListTile(
          leftText: 'Transaction Type',
          rightText: widget.transaction.category ?? "",
          reightTextStyle: FontTypography.text16.bold.withCustomColor(
            AppColors.primaryBlue,
          ),
        ),
        TransactionDetailsListTile(
          leftText: 'Fees',
          rightText:
              "${widget.transaction.fees ?? ""} ${widget.transaction.currency?.code ?? ""}",
        ),
        if (widget.transaction.rateFormat != null)
          AnimatedSize(
            duration: Duration(milliseconds: 400),
            child: hideExchangeDetails
                ? SizedBox.shrink()
                : TransactionDetailsListTile(
                    leftText: 'Exchange Rate',
                    rightText: widget.transaction.rateFormat ?? '',
                  ),
          ),

        Column(
          children: [
            TransactionDetailsListTile(
              leftText: 'Date/Time',
              rightText: AppUtils.formatDateTime(
                  (widget.transaction.createdAt ?? DateTime.now())
                      .toLocal()
                      .toString()),
            ),

            if (widget.transaction.source != null)
              TransactionDetailsListTile(
                leftText: 'Source',
                rightText: widget.transaction.source ?? "",
              ),
            // if (transaction.category?.toLowerCase() ==
            //     "transfer")
            //   TransactionDetailsListTile(
            //     leftText: 'Sender',
            //     rightText: context
            //             .read<AuthUserVM>()
            //             .user
            //             ?.fullName ??
            //         '',
            //   ),
            if (widget.transaction.destination != null &&
                widget.transaction.category?.toLowerCase() != "transfer")
              TransactionDetailsListTile(
                leftText: 'Destination',
                rightText: widget.transaction.destination ?? "",
              ),
            if (widget.transaction.sessionId != null)
              TransactionDetailsListTile(
                leftText: 'Session ID',
                rightText: widget.transaction.sessionId ?? "",
                onCopy: () {
                  Clipboard.setData(
                      ClipboardData(text: widget.transaction.sessionId ?? ""));

                  FlushBarToast.fLSnackBar(
                    message: "Session ID copied",
                    snackBarType: SnackBarType.success,
                  );
                },
              ),
            TransactionDetailsListTile(
              showBorder: widget.transaction.interacSecurityAnswer != null
                  ? true
                  : false,
              leftText: 'Reference',
              rightText: widget.transaction.reference ?? "",
              onCopy: () {
                Clipboard.setData(
                    ClipboardData(text: widget.transaction.reference ?? ""));

                FlushBarToast.fLSnackBar(
                  message: "Transaction reference copied",
                  snackBarType: SnackBarType.success,
                );
              },
            ),
            if (widget.transaction.interacSecurityAnswer != null)
              TransactionDetailsListTile(
                showBorder: false,
                leftText: 'Interac Security Answer',
                rightText: widget.transaction.interacSecurityAnswer ?? "",
              ),
          ],
        ),

        // Hide if category is conversion
        if (widget.transaction.category?.toLowerCase() != "conversion" &&
            !sameCurrencyTransfer)
          Padding(
            padding: EdgeInsets.only(
              top: Sizer.width(24),
            ),
            child: Row(
              children: [
                CustomSwitch(
                  value: hideExchangeDetails,
                  onChanged: (v) {
                    hideExchangeDetails = v;
                    setState(() {});
                  },
                ),
                XBox(12),
                Text(
                  "Hide Exchange details",
                  style: FontTypography.text14.withCustomColor(
                    AppColors.gray79,
                  ),
                )
              ],
            ),
          ),
        const YBox(25),
      ],
    );
  }
}
