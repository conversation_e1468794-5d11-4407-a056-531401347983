import 'package:korrency/core/core.dart';

class UserAvatar extends StatelessWidget {
  const UserAvatar({
    super.key,
    required this.avatarUrl,
    this.size,
    this.onTap,
  });

  final String avatarUrl;
  final double? size;
  final Function()? onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        height: Sizer.height(size ?? 92),
        width: Sizer.width(size ?? 92),
        decoration: BoxDecoration(
          color: AppColors.white,
          borderRadius: BorderRadius.circular(100),
        ),
        child: cacheNetWorkImage(
          avatarUrl,
          fit: BoxFit.cover,
        ),
      ),
    );
  }
}
