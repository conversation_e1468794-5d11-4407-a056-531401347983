import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class SelectWalletCurrencySheet extends StatefulWidget {
  const SelectWalletCurrencySheet({
    super.key,
    this.fromConvert = false,
    this.isSendMoney = false,
    this.selectedCurrencyCode,
  });

  final bool fromConvert; // TOP Selection
  final bool isSendMoney;
  final String? selectedCurrencyCode;

  @override
  State<SelectWalletCurrencySheet> createState() =>
      _SelectWalletCurrencySheetState();
}

class _SelectWalletCurrencySheetState extends State<SelectWalletCurrencySheet> {
  @override
  Widget build(BuildContext context) {
    return Container(
        height: Sizer.screenHeight * 0.46,
        margin: EdgeInsets.all(
          Sizer.radius(12),
        ),
        padding: EdgeInsets.symmetric(
          horizontal: Sizer.width(12),
        ),
        decoration: BoxDecoration(
          color: AppColors.bgWhite,
          borderRadius: BorderRadius.circular(Sizer.radius(12)),
        ),
        child: ListView(
          children: [
            const YBox(20),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  "Select Currency",
                  style: AppTypography.text22.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            // const YBox(25),
            Consumer<WalletVM>(builder: (context, vm, _) {
              return ListView.separated(
                padding: EdgeInsets.symmetric(
                  vertical: Sizer.height(20),
                ),
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: vm.walletList.length,
                itemBuilder: (context, i) {
                  final wallet = vm.walletList[i];
                  final isSelected =
                      wallet.currency?.code == widget.selectedCurrencyCode;
                  return CurrencyItemSelector(
                    country: CountryModel(
                      name: wallet.currency?.name,
                      code: wallet.currency?.code,
                      flag: wallet.currency?.flag,
                      dialCode:
                          "${wallet.currency?.symbol}${AppUtils.formatAmountString(wallet.balance ?? "0")}",
                    ),
                    isSelected: isSelected,
                    onTap: () {
                      if (widget.isSendMoney) {
                        sendMoneyCurrencySelection(i);
                      } else {
                        handleCurrencySelection(i, vm);
                      }
                    },
                  );
                },
                separatorBuilder: (ctx, i) => const YBox(8),
              );
            }),
            const YBox(100),
          ],
        ));
  }

  void handleCurrencySelection(int index, WalletVM walletVM) {
    var convertMoneyVM = context.read<ConvertMoneyVM>();
    var walletVM = context.read<WalletVM>();

    if (widget.fromConvert) {
      convertMoneyVM.setFromConvertWallet(
          walletVM.walletList[index].currency?.code ?? "");
      // convertMoneyVM.setToConvertWallet(
      //     walletVM.walletList[index .currency?== 0 ? 1 : 0].code ?? "");
    } else {
      convertMoneyVM
          .setToConvertWallet(walletVM.walletList[index].currency?.code ?? "");
      // convertMoneyVM.setFromConvertWallet(
      //     walletVM.walletList[index .currency?== 0 ? 1 : 0].code ?? "");
    }
    // walletVM.getConversionRate();
    convertMoneyVM
        // ..resetData()
        .getConversionRate(isFrom: widget.fromConvert);
    Navigator.pop(context);
  }

  void sendMoneyCurrencySelection(int index) {
    var sendMoneyVM = context.read<SendMoneyVM>();
    var walletVM = context.read<WalletVM>();

    if (widget.fromConvert) {
      sendMoneyVM.setFromConvertWallet(
        code: walletVM.walletList[index].currency?.code ?? "",
        walletList: walletVM.walletList,
      );

      // Set Recipient Currency (Bottom Selection) to the selected wallet currency
      // sendMoneyVM.setRecipientCurrency(walletVM.walletList[index].currency); //TODO: check it out
    } else {
      if (walletVM.walletList[index].currency != null) {
        sendMoneyVM.setRecipientCurrency(walletVM.walletList[index].currency!);
      }
    }
    sendMoneyVM
      ..getConversionRate()
      ..getFees();
    Navigator.pop(context);
  }
}
