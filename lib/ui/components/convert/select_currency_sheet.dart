import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class SelectCurrencySheet extends StatefulWidget {
  const SelectCurrencySheet({
    super.key,
    this.selectedCurrencyCode,
  });

  final String? selectedCurrencyCode;

  @override
  State<SelectCurrencySheet> createState() => _SelectCurrencySheetState();
}

class _SelectCurrencySheetState extends State<SelectCurrencySheet> {
  final _searchC = TextEditingController();
  List<Currency> _filteredCurrencies = [];

  @override
  void initState() {
    super.initState();

    // fromConvertWallet recipientCurrencies
    _filteredCurrencies = context
            .read<SendMoneyVM>()
            .fromConvertWallet
            ?.currency
            ?.recipientCurrencies ??
        [];
    _searchC.addListener(_onSearchChanged);
  }

  void _onSearchChanged() {
    final query = _searchC.text.toLowerCase().trim();
    final currencies = context
            .read<SendMoneyVM>()
            .fromConvertWallet
            ?.currency
            ?.recipientCurrencies ??
        [];
    setState(() {
      if (query.isEmpty) {
        _filteredCurrencies = currencies;
      } else {
        _filteredCurrencies = currencies.where((currency) {
          final name = currency.name?.toLowerCase() ?? '';
          final code = currency.code?.toLowerCase() ?? '';

          return name.contains(query) || code.contains(query);
        }).toList();
      }
    });
  }

  @override
  void dispose() {
    _searchC.dispose();
    _searchC.removeListener(_onSearchChanged);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: Sizer.screenHeight * 0.8,
      margin: EdgeInsets.all(
        Sizer.radius(12),
      ),
      padding: EdgeInsets.symmetric(
        horizontal: Sizer.width(12),
      ),
      decoration: BoxDecoration(
        color: AppColors.bgWhite,
        borderRadius: BorderRadius.circular(Sizer.radius(12)),
      ),
      child: Column(
        children: [
          const YBox(20),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                "Select Currency",
                style: AppTypography.text22.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const YBox(20),
          CustomTextField(
            controller: _searchC,
            prefixIcon: Padding(
              padding: EdgeInsets.all(Sizer.radius(12)),
              child: SvgPicture.asset(AppSvgs.search),
            ),
            hintText: "Search currency",
            keyboardType: KeyboardType.regular,
            inputFormatters: [],
            borderRadius: Sizer.height(12),
            onSubmitted: (val) {
              // Optional: Handle search submission
            },
          ),
          Consumer<CurrencyVM>(builder: (context, vm, _) {
            final sendMoneyVM = context.read<SendMoneyVM>();

            return Expanded(
              child: ListView.separated(
                padding: EdgeInsets.only(
                  top: Sizer.height(20),
                  bottom: Sizer.height(80),
                ),
                shrinkWrap: true,
                itemCount: _filteredCurrencies.length,
                itemBuilder: (context, i) {
                  final currency = _filteredCurrencies[i];

                  return CurrencyItemSelector(
                    onTap: () {
                      switch (currency.code?.toLowerCase()) {
                        case "xof":
                          printty("XOF  TransactionInitiated_XOF ");
                          MixpanelService().track("TransactionInitiated_XOF");
                          break;
                        case "xaf":
                          printty("XAF  TransactionInitiated_XAF ");
                          MixpanelService().track("TransactionInitiated_XAF");
                          break;
                      }
                      sendMoneyVM.setRecipientCurrency(currency);
                      Navigator.pop(context);
                    },
                    isSelected: currency.code == widget.selectedCurrencyCode,
                    country: CountryModel(
                      code: currency.code ?? '',
                      name: currency.country,
                      flag: currency.flag,
                      dialCode: currency.code,
                    ),
                  );
                  // return InkWell(
                  //   onTap: () {
                  //     sendMoneyVM.setRecipientCurrency(sendMoneyCurrencies![i]);
                  //     Navigator.pop(context);
                  //   },
                  //   child: ContainerWithBluewishBg(
                  //     padding: EdgeInsets.symmetric(
                  //       vertical: Sizer.height(20),
                  //       horizontal: Sizer.width(16),
                  //     ),
                  //     child: WalletListTile(
                  //       title: sendMoneyCurrencies?[i].code ?? '',
                  //       currencyIcon: sendMoneyCurrencies?[i].flag ?? '',
                  //       useNetworkSvg: true,
                  //       showTrailingWidget: false,
                  //       icon: Icons.check_circle,
                  //     ),
                  //   ),
                  // );
                },
                separatorBuilder: (context, index) => const YBox(8),
              ),
            );
          }),
        ],
      ),
    );
  }
}
