import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';
import 'package:korrency/ui/screens/auth/kyc/success_sheet.dart';

class VerifyIdentifyModal extends StatefulWidget {
  const VerifyIdentifyModal({super.key});

  @override
  State<VerifyIdentifyModal> createState() => _VerifyIdentifyModalState();
}

class _VerifyIdentifyModalState extends State<VerifyIdentifyModal>
    with TickerProviderStateMixin {
  late AnimationController _slideController;
  late AnimationController _iconController;
  late AnimationController _contentController;
  late AnimationController _listController;
  
  late Animation<Offset> _slideAnimation;
  late Animation<double> _iconAnimation;
  late Animation<double> _contentAnimation;
  late Animation<double> _listAnimation;

  @override
  void initState() {
    super.initState();
    
    // Initialize animation controllers
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    
    _iconController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _contentController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    
    _listController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    // Initialize animations
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutBack,
    ));
    
    _iconAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _iconController,
      curve: Curves.elasticOut,
    ));
    
    _contentAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _contentController,
      curve: Curves.easeIn,
    ));
    
    _listAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _listController,
      curve: Curves.easeInOut,
    ));
    
    // Start animations with staggered timing
    _startAnimations();
  }
  
  void _startAnimations() async {
    _slideController.forward();
    await Future.delayed(const Duration(milliseconds: 200));
    _iconController.forward();
    await Future.delayed(const Duration(milliseconds: 300));
    _contentController.forward();
    await Future.delayed(const Duration(milliseconds: 200));
    _listController.forward();
  }

  @override
  void dispose() {
    _slideController.dispose();
    _iconController.dispose();
    _contentController.dispose();
    _listController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(Sizer.radius(12)),
      child: SizedBox(
        // height: Sizer.screenHeight * 0.88,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            SlideTransition(
              position: _slideAnimation,
              child: Container(
                width: double.infinity,
                padding: EdgeInsets.symmetric(horizontal: Sizer.width(20)),
                decoration: BoxDecoration(
                  color: AppColors.bgWhite,
                  borderRadius: BorderRadius.circular(12),
                ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  YBox(20),
                  ScaleTransition(
                    scale: _iconAnimation,
                    child: SvgPicture.asset(
                      AppSvgs.docIdentity,
                      height: Sizer.height(132),
                    ),
                  ),
                  YBox(20),
                  FadeTransition(
                    opacity: _contentAnimation,
                    child: Column(
                      children: [
                        Text(
                          'Verify your Identity',
                          textAlign: TextAlign.center,
                          style: FontTypography.text22.semiBold,
                        ),
                        YBox(4),
                        Text(
                          'To protect your account, we need a few documents from you',
                          textAlign: TextAlign.center,
                          style: FontTypography.text16.withCustomColor(
                            AppColors.gray93,
                          ),
                        ),
                      ],
                    ),
                  ),
                  YBox(30),
                  FadeTransition(
                    opacity: _listAnimation,
                    child: Column(
                      children: [
                        IdentityListTile(
                          iconPath: AppSvgs.license,
                          title: 'Driver\'s License',
                          subTitle: 'Upload your License to verify',
                        ),
                        Divider(color: AppColors.grayBEB),
                        IdentityListTile(
                          iconPath: AppSvgs.permit,
                          title: 'Resident\'s Permit',
                          subTitle: 'Use your permit to confirm your identity',
                        ),
                        Divider(color: AppColors.grayBEB),
                        IdentityListTile(
                          iconPath: AppSvgs.passport,
                          title: 'International Passport',
                          subTitle: 'Upload your passport to confirm your identity',
                        ),
                        Divider(color: AppColors.grayBEB),
                        IdentityListTile(
                          iconPath: AppSvgs.license,
                          title: 'National Identity Card',
                          subTitle: 'Use your Goverment issued ID to verify',
                        ),
                      ],
                    ),
                  ),
                  YBox(30),
                  FadeTransition(
                    opacity: _contentAnimation,
                    child: CustomBtn.solid(
                      borderRadius: BorderRadius.circular(Sizer.radius(20)),
                      onTap: () {
                        _startTheOnfidoWorkflow();
                      },
                      text: 'Verify Identity',
                    ),
                  ),
                  YBox(30),
                ],
              ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  _startTheOnfidoWorkflow() {
    var kycVM = context.read<KycVM>();
    kycVM.generateOnfidoToken().then((value) {
      if (value.success) {
        printty("Onfido Token Generated");
        kycVM
            .startOnfidoWorkflow(
          sdkToken: kycVM.onfidoData!.sdkToken!,
          workFlowRunId: kycVM.onfidoData!.workflowRunId!,
        )
            .then((value) async {
          if (value) {
            printty("Onfido Workflow Completed");
            kycVM.updateOnfidoUser();

            // Track KYC completion
            try {
              final user = context.read<AuthUserVM>().user;
              final userId = user?.id ?? 'unknown';
              final workflowRunId = kycVM.onfidoData?.workflowRunId ?? '';

              // Use Future.microtask to avoid BuildContext across async gaps
              Future.microtask(() async {
                await UnifiedAnalyticsManager.instance.trackCompleteKyc(
                  userId: userId,
                  kycLevel: 'identity_verification',
                  verificationMethod: 'onfido_document_upload',
                  additionalParameters: {
                    'completion_timestamp': DateTime.now().toIso8601String(),
                    'workflow_run_id': workflowRunId,
                  },
                );
              });
            } catch (e) {
              printty('❌ Error tracking KYC completion: $e');
            }

            _gotoNext();
          } else {
            printty("Something went wrong 1");
            _somethingWentWrong();
          }
        });
      } else {
        _somethingWentWrong();
        printty("Something went wrong 2");
      }
    });
  }

  _gotoNext() {
    Navigator.pop(context);
    BsWrapper.bottomSheet(
      context: context,
      widget: SuccessSheet(
        arg: ConfirmationArg(
          title: "Verification Successful",
          buttonText: 'Home',
          subtitle: const ConfirmationSubtitleText(
            startText: "Your",
            endText: " documents have been sent and is undergoing review.",
          ),
          onBtnTap: () {
            // Navigator.pop(NavigatorKeys.appNavigatorKey.currentContext!);
            Navigator.pushReplacementNamed(
                NavigatorKeys.appNavigatorKey.currentContext!,
                RoutePath.dashboardNav);
          },
        ),
      ),
    );
  }

  _somethingWentWrong() {
    BsWrapper.bottomSheet(
      context: context,
      widget: SuccessSheet(
        arg: ConfirmationArg(
            title: "Something went wrong.",
            buttonText: 'Continue',
            imagePath: AppImages.circleError,
            subtitle: const ConfirmationSubtitleText(
              startText: "We don’t ",
              endText:
                  " really know what’s wrong. Please try again and continue.",
            ),
            onBtnTap: () {
              Navigator.pop(NavigatorKeys.appNavigatorKey.currentContext!);
              Navigator.pop(NavigatorKeys.appNavigatorKey.currentContext!);
            }),
      ),
    );
  }
}

class IdentityListTile extends StatelessWidget {
  const IdentityListTile({
    super.key,
    required this.iconPath,
    required this.title,
    required this.subTitle,
  });

  final String iconPath;
  final String title;
  final String subTitle;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Container(
          padding: EdgeInsets.all(Sizer.radius(15)),
          decoration: BoxDecoration(
            color: AppColors.blueFFE,
            borderRadius: BorderRadius.circular(Sizer.radius(12)),
          ),
          child: SvgPicture.asset(
            iconPath,
            height: Sizer.height(30),
          ),
        ),
        XBox(6),
        Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: FontTypography.text14.withCustomColor(
                AppColors.primaryBlue,
              ),
            ),
            YBox(6),
            Text(
              subTitle,
              style: FontTypography.text12.withCustomColor(
                AppColors.gray79,
              ),
            ),
          ],
        ),
      ],
    );
  }
}
