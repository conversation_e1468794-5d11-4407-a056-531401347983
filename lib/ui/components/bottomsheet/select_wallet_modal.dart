import 'package:korrency/core/core.dart';

class SelectWalletModal extends StatefulWidget {
  const SelectWalletModal({super.key, this.exportWallet = false});

  final bool exportWallet;

  @override
  State<SelectWalletModal> createState() => _SelectWalletModalState();
}

class _SelectWalletModalState extends State<SelectWalletModal> {
  @override
  Widget build(BuildContext context) {
    return Consumer<WalletVM>(builder: (context, walletVm, _) {
      return Padding(
        padding: EdgeInsets.all(Sizer.radius(12)),
        child: SizedBox(
          height: Sizer.screenHeight * 0.46,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Expanded(
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: Sizer.width(12)),
                  decoration: BoxDecoration(
                    color: AppColors.bgWhite,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Column(
                    children: [
                      YBox(20),
                      Text(
                        "Select Wallet",
                        style: AppTypography.text22.semiBold,
                      ),
                      YBox(20),
                      Expanded(
                        child: ListView.separated(
                          shrinkWrap: true,
                          padding: EdgeInsets.only(
                            top: Sizer.height(10),
                            bottom: Sizer.height(20),
                          ),
                          itemCount: walletVm.walletList.length,
                          separatorBuilder: (_, __) => YBox(8),
                          itemBuilder: (ctx, i) {
                            final wallet = walletVm.walletList[i];
                            return SelectWalletListTile(
                              name: wallet.currency?.name ?? "",
                              symbol: wallet.currency?.symbol ?? "",
                              amount: AppUtils.formatAmountDoubleString(
                                  wallet.balance ?? ""),
                              flag: wallet.currency?.flag ?? "",
                              onTap: () {
                                if (widget.exportWallet) {
                                  Navigator.pop(context, wallet);
                                  return;
                                } else {
                                  Navigator.pop(context, wallet.currency?.code);
                                  // BsWrapper.bottomSheet(
                                  //   context: context,
                                  //   widget: FundYourWalletModal(
                                  //     currencyCode: wallet.currency?.code ?? "",
                                  //   ),
                                  // );
                                }
                              },
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    });
  }
}

class SelectWalletListTile extends StatelessWidget {
  const SelectWalletListTile({
    super.key,
    this.isSelected = false,
    required this.name,
    required this.symbol,
    required this.amount,
    required this.flag,
    this.onTap,
  });

  final bool isSelected;
  final String name;
  final String symbol;
  final String amount;
  final String flag;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(
          vertical: Sizer.height(20),
          horizontal: Sizer.width(12),
        ),
        decoration: BoxDecoration(
          color: AppColors.bgWhite,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(30),
              child: SizedBox(
                width: Sizer.width(28),
                height: Sizer.height(28),
                child: SvgPicture.network(
                  flag,
                  fit: BoxFit.cover,
                ),
              ),
            ),
            XBox(8),
            Expanded(
              child: Text(
                name,
                style: AppTypography.text16.medium.copyWith(
                  color: AppColors.gray79,
                ),
              ),
            ),
            XBox(10),
            RichText(
              text: TextSpan(
                style: AppTypography.text16.medium.copyWith(
                  color: AppColors.mainBlack,
                  fontFamily: AppFont.outfit.family,
                ),
                children: [
                  TextSpan(
                      text: symbol,
                      style: AppTypography.text16.medium.copyWith(
                        color: AppColors.mainBlack,
                        fontFamily: AppFont.inter.family,
                      )),
                  TextSpan(text: amount),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
