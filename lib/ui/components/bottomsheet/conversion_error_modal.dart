import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class ConversionErrorModal extends StatefulWidget {
  const ConversionErrorModal({
    super.key,
  });

  @override
  State<ConversionErrorModal> createState() => _ConversionErrorModalState();
}

class _ConversionErrorModalState extends State<ConversionErrorModal> {
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(Sizer.radius(12)),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            padding: EdgeInsets.only(
              left: Sizer.width(20),
              right: Sizer.width(20),
            ),
            decoration: BoxDecoration(
              color: AppColors.bgWhite,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const YBox(28),
                SvgPicture.asset(
                  AppSvgs.convertError,
                  height: Sizer.height(200),
                ),
                const YBox(40),
                Text(
                  'Your Conversion Failed!',
                  style: FontTypography.text22.semiBold,
                ),
                YBox(4),
                Text(
                  "We couldn’t complete your conversion, \nTry again later.",
                  textAlign: TextAlign.center,
                  style: AppTypography.text16.copyWith(
                    color: AppColors.gray93,
                    fontFamily: AppFont.outfit.family,
                  ),
                ),
                YBox(24),
                YBox(30),
                CustomBtn.solid(
                  onTap: () {
                    var convertMoneyVM = context.read<ConvertMoneyVM>();
                    convertMoneyVM.resetData();
                    Navigator.pushNamedAndRemoveUntil(
                      NavigatorKeys.appNavigatorKey.currentContext!,
                      RoutePath.dashboardNav,
                      (route) => false,
                    );
                  },
                  online: true,
                  borderRadius: BorderRadius.circular(Sizer.radius(14)),
                  textStyle: FontTypography.text15.medium.withCustomColor(
                    AppColors.white,
                  ),
                  text: "Done",
                ),
                YBox(30),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
