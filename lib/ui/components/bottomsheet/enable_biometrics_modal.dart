// ignore_for_file: use_build_context_synchronously

import 'package:korrency/core/core.dart';
import 'package:korrency/session_manager.dart';
import 'package:korrency/ui/components/components.dart';
import 'package:local_session_timeout/local_session_timeout.dart';

class EnableBiometricsModal extends StatefulWidget {
  const EnableBiometricsModal({super.key});

  @override
  State<EnableBiometricsModal> createState() => _EnableBiometricsModalState();
}

class _EnableBiometricsModalState extends State<EnableBiometricsModal> {
  FundWallletType? fundWallletType;
  @override
  Widget build(BuildContext context) {
    return Consumer<WalletVM>(builder: (context, walletVm, _) {
      return Padding(
        padding: EdgeInsets.all(Sizer.radius(12)),
        child: SizedBox(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: double.infinity,
                padding: EdgeInsets.symmetric(horizontal: Sizer.width(24)),
                decoration: BoxDecoration(
                  color: AppColors.bgWhite,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Column(
                  children: [
                    YBox(40),
                    Image.asset(
                      AppImages.locKey,
                      height: Sizer.height(228),
                    ),
                    YBox(20),
                    Text(
                      "Enable Biometrics",
                      style: AppTypography.text22.semiBold,
                    ),
                    YBox(4),
                    Text(
                      "Make logging in fasterby enabling your Fingerprint or Face ID",
                      textAlign: TextAlign.center,
                      style: AppTypography.text16.copyWith(
                        color: AppColors.gray93,
                      ),
                    ),
                    YBox(60),
                    CustomBtn.solid(
                      borderRadius: BorderRadius.circular(Sizer.radius(20)),
                      onTap: () {
                        updateFingerPrint();
                      },
                      text: "Enable Biometrics",
                    ),
                    YBox(20),
                    TextButton(
                        onPressed: () {
                          sessionStateStream.add(SessionState.startListening);
                          StorageService.removeStringItem(
                              StorageKey.logoutCount);
                          Navigator.pop(context);
                        },
                        child: Text(
                          "Do this Later",
                          style: AppTypography.text15.medium.copyWith(
                            color: AppColors.primaryBlue90,
                          ),
                        )),
                    YBox(30)
                  ],
                ),
              ),
            ],
          ),
        ),
      );
    });
  }

  updateFingerPrint() async {
    await BiometricService.authenticate().then((value) {
      if (value) {
        StorageService.storeBoolItem(StorageKey.fingerPrintIsEnabled, value);
        BsWrapper.showCustomDialog(
          context,
          child: ConfirmationDialog(
            args: ConfirmationDialogArgs(
              title: "Biometrics Enabled",
              content: "Your biometrics has been enabled successfully",
              btnText: "Done",
              onTap: () {
                final ctx = NavigatorKeys.appNavigatorKey.currentContext;
                Navigator.pop(ctx!);
              },
            ),
          ),
        );
      } else {
        FlushBarToast.fLSnackBar(
          message: "Biometric Authentication Failed",
        );
      }
    });
  }
}
