import 'package:korrency/core/core.dart';

final List<String> walletBg = [
  AppImages.cad,
  AppImages.ngn,
  AppImages.ghs,
  AppImages.kes,
  AppImages.usd,
  AppImages.other,
];

String getWalletBg(String code) {
  try {
    return walletBg.firstWhere((w) => w.contains(code.toLowerCase()));
  } catch (e) {
    // Return a default wallet background if no match is found
    return walletBg.last; // Returns the 'other' background
  }
}

class WalletCard extends StatelessWidget {
  const WalletCard({
    super.key,
    required this.currency,
    required this.flag,
    required this.amount,
    required this.symbol,
    required this.amountFraction,
    this.isPrimary = false,
    required this.bottomTitle,
    required this.bottomSubTitle,
    required this.bgImage,
    this.textColor,
    this.scale = 1.0,
  });

  final String currency;
  final String flag;
  final String amountFraction;
  final String amount;
  final String symbol;
  final bool isPrimary;
  final String bottomTitle;
  final String bottomSubTitle;
  final String bgImage;
  final Color? textColor;
  final double scale;

  @override
  Widget build(BuildContext context) {
    return Transform.scale(
      scale: scale,
      child: Container(
        padding: EdgeInsets.only(
          top: Sizer.height(6),
          left: Sizer.width(6),
          right: Sizer.width(6),
        ),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(Sizer.radius(16)),
          color: AppColors.white,
          // border: Border(
          //   top: BorderSide(color: AppColors.white, width: Sizer.width(6)),
          //   left: BorderSide(color: AppColors.white, width: Sizer.width(6)),
          //   right: BorderSide(color: AppColors.white, width: Sizer.width(6)),
          // ),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(Sizer.radius(10)),
          child: Container(
            height: Sizer.height(164),
            // margin: const EdgeInsets.symmetric(horizontal: 4),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(Sizer.radius(10)),
              image: DecorationImage(
                image: AssetImage(bgImage),
                fit: BoxFit.cover,
              ),
              // boxShadow: [
              //   BoxShadow(
              //     color: Colors.black12.withValues(alpha: .1),
              //     blurRadius: 12,
              //     offset: const Offset(0, 4),
              //   ),
              // ],
            ),
            child: Padding(
              padding: EdgeInsets.symmetric(
                horizontal: Sizer.width(28),
                vertical: Sizer.height(16),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Container(
                          decoration: BoxDecoration(
                            border: Border.all(
                              color: AppColors.white,
                              width: 1,
                            ),
                            borderRadius:
                                BorderRadius.circular(Sizer.radius(2)),
                          ),
                          child: SvgPicture.network(flag,
                              height: Sizer.height(16))),
                      XBox(4),
                      Text(
                        currency,
                        style: AppTypography.text12.copyWith(
                          color: textColor ?? Colors.white,
                        ),
                      ),
                      Spacer(),
                      if (!isPrimary)
                        RichText(
                          text: TextSpan(
                            children: [
                              TextSpan(
                                text: symbol,
                                style: AppTypography.text18.copyWith(
                                  color: textColor ?? AppColors.white,
                                  fontFamily: AppFont.inter.family,
                                ),
                              ),
                              TextSpan(
                                text: amount,
                                style: AppTypography.text18.copyWith(
                                  color: textColor ?? AppColors.white,
                                  fontFamily: AppFont.outfit.family,
                                ),
                              ),
                              if (amountFraction.isNotEmpty)
                                TextSpan(
                                  text: ".",
                                  style: AppTypography.text18.copyWith(
                                    color: textColor ?? AppColors.white,
                                    fontFamily: AppFont.outfit.family,
                                  ),
                                ),
                              TextSpan(
                                text: amountFraction,
                                style: AppTypography.text14.copyWith(
                                  color: textColor ?? AppColors.white,
                                  fontFamily: AppFont.outfit.family,
                                ),
                              ),
                            ],
                          ),
                        ),
                    ],
                  ),
                  YBox(12),
                  RichText(
                    text: TextSpan(
                      children: [
                        TextSpan(
                          text: symbol,
                          style: AppTypography.text26.copyWith(
                            color: textColor ?? AppColors.white,
                            fontFamily: AppFont.outfit.family,
                          ),
                        ),
                        TextSpan(
                          text: amount,
                          style: AppTypography.text26.copyWith(
                            color: textColor ?? AppColors.white,
                            fontFamily: AppFont.outfit.family,
                          ),
                        ),
                        if (amountFraction.isNotEmpty)
                          TextSpan(
                            text: ".",
                            style: AppTypography.text26.copyWith(
                              color: textColor ?? AppColors.white,
                              fontFamily: AppFont.outfit.family,
                            ),
                          ),
                        TextSpan(
                          text: amountFraction,
                          style: AppTypography.text18.copyWith(
                            color: textColor ?? AppColors.white,
                            fontFamily: AppFont.outfit.family,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Spacer(),
                  Text(
                    bottomTitle,
                    style: AppTypography.text12.copyWith(
                      color: textColor ?? Colors.white,
                    ),
                  ),
                  YBox(4),
                  Text(
                    bottomSubTitle,
                    style: AppTypography.text16.copyWith(
                      color: textColor ?? Colors.white,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
