import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';
import 'package:share_plus/share_plus.dart';

class GbpAccountDetailsWidget extends StatelessWidget {
  const GbpAccountDetailsWidget({
    super.key,
    required this.wallet,
  });

  final Wallet wallet;

  @override
  Widget build(BuildContext context) {
    return Consumer<WalletVM>(builder: (context, vm, _) {
      return Container(
        padding: EdgeInsets.symmetric(
          horizontal: Sizer.width(16),
          vertical: Sizer.height(20),
        ),
        decoration: BoxDecoration(
          color: AppColors.blueBFF,
          borderRadius: BorderRadius.circular(Sizer.radius(10)),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            NgnColText(
              title: "Account name",
              value: wallet.virtualAccounts?[0].accountName ?? "",
            ),
            YBox(20),
            // Row(
            //   children: [
            //     Expanded(
            //       child: NgnColText(
            //         title: "Account number",
            //         value: wallet.virtualAccounts?[0].customerIdentifier ?? "",
            //       ),
            //     ),
            //     CopyBtn(
            //       onCopy: () {
            //         Clipboard.setData(ClipboardData(
            //             text: wallet.virtualAccounts?[0].accountNumber ?? ""));
            //         showSuccessToastMessage("Account number copied");
            //       },
            //     ),
            //   ],
            // ),
            // YBox(20),
            // Row(
            //   children: [
            //     Expanded(
            //       child: NgnColText(
            //         title: "Sort Code",
            //         value: wallet.virtualAccounts?[0].bankCode ?? "",
            //       ),
            //     ),
            //     CopyBtn(
            //       onCopy: () {
            //         Clipboard.setData(ClipboardData(
            //             text: wallet.virtualAccounts?[0].accountNumber ?? ""));
            //         showSuccessToastMessage("Account number copied");
            //       },
            //     ),
            //   ],
            // ),
            // YBox(20),
            Row(
              children: [
                Expanded(
                  child: NgnColText(
                    title: "IBAN",
                    value: wallet.virtualAccounts?[0].accountNumber ?? "",
                  ),
                ),
                CopyBtn(
                  text: wallet.virtualAccounts?[0].accountNumber ?? "",
                ),
              ],
            ),
            YBox(28),
            CustomBtn.solid(
              borderRadius: BorderRadius.circular(Sizer.radius(14)),
              height: Sizer.height(40),
              isOutline: true,
              onlineColor: AppColors.white,
              textColor: AppColors.primaryBlue,
              onTap: () {
                Share.share(
                  "IBAN: ${wallet.virtualAccounts?[0].accountNumber} \nAccount Name: ${wallet.virtualAccounts?[0].accountName}",
                  subject: "Korrency",
                );
              },
              text: "Share details",
            )
          ],
        ),
      );
    });
  }
}
