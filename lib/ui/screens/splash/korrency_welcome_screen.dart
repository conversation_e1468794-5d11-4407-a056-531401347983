import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class KorrencyWelcomeScreen extends StatefulWidget {
  const KorrencyWelcomeScreen({super.key});

  @override
  State<KorrencyWelcomeScreen> createState() => _KorrencyWelcomeScreenState();
}

class _KorrencyWelcomeScreenState extends State<KorrencyWelcomeScreen>
    with TickerProviderStateMixin {
  late AnimationController _customController;
  late Animation<double> _customAnimation;

  @override
  void initState() {
    super.initState();
    _customController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    _customAnimation = CurvedAnimation(
      parent: _customController,
      curve: Curves.easeInOut,
    );

    // Start form animation after a delay
    Future.delayed(const Duration(milliseconds: 300), () {
      _customController.forward();
    });
  }

  @override
  void dispose() {
    _customController.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: FadeTransition(
        opacity: _customAnimation,
        child: SlideTransition(
          position: _customAnimation.drive(
            Tween<Offset>(
              begin: const Offset(0, 0.2),
              end: const Offset(0, 0),
            ),
          ),
          child: Column(
            children: [
              Container(
                height: Sizer.screenHeight * 0.56,
                decoration: BoxDecoration(
                  image: DecorationImage(
                    image: AssetImage(AppImages.welcomeBg),
                    fit: BoxFit.cover,
                  ),
                ),
                child: Center(
                  child: SizedBox(
                    height: Sizer.height(240),
                    child: Image.asset(AppImages.welcome),
                  ),
                ),
              ),
              YBox(20),
              Container(
                padding: EdgeInsets.symmetric(
                  horizontal: Sizer.width(24),
                ),
                child: Column(
                  children: [
                    Text(
                      "Welcome to Korrency",
                      style: AppTypography.text26.semiBold.copyWith(
                        fontFamily: AppFont.outfit.family,
                        color: AppColors.black900,
                      ),
                    ),
                    const YBox(8),
                    Text(
                      "Your Secure money exchange Platform",
                      textAlign: TextAlign.center,
                      style: AppTypography.text16.copyWith(
                        fontFamily: AppFont.outfit.family,
                        color: AppColors.grayAB,
                      ),
                    ),
                    const YBox(70),
                    CustomBtn.solid(
                      borderRadius: BorderRadius.circular(20),
                      onTap: () {
                        Navigator.of(context)
                            .pushNamed(RoutePath.createAcctScreen);
                      },
                      online: true,
                      text: "Get Started",
                    ),
                    const YBox(16),
                    CustomBtn.solid(
                      isOutline: true,
                      textColor: AppColors.primaryBlue,
                      borderRadius: BorderRadius.circular(20),
                      onTap: () {
                        Navigator.of(context).pushNamed(RoutePath.loginScreen);
                      },
                      text: "Sign in",
                    ),
                    const YBox(30),
                  ],
                ),
              )
            ],
          ),
        ),
      ),
    );
  }
}
