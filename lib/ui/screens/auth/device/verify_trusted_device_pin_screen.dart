import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';
import 'package:korrency/ui/screens/dashboard/confrimation/success_confirm_screen.dart';
import 'package:pinput/pinput.dart';

class VerifyTrustedDevicePinScreen extends StatefulWidget {
  const VerifyTrustedDevicePinScreen({super.key});

  @override
  State<VerifyTrustedDevicePinScreen> createState() =>
      _VerifyTrustedDevicePinScreenState();
}

class _VerifyTrustedDevicePinScreenState
    extends State<VerifyTrustedDevicePinScreen> {
  final FocusNode pinFocusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    pinFocusNode.requestFocus();
    KeyboardOverlay.addRemoveFocusNode(context, pinFocusNode);
  }

  @override
  void dispose() {
    pinFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<TrustedDeviceVM>(
      builder: (context, vm, _) {
        return BusyOverlay(
          show: vm.isBusy,
          child: Scaffold(
            backgroundColor: AppColors.bgWhite,
            body: SafeArea(
              bottom: false,
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: Sizer.width(24))
                    .copyWith(top: Sizer.height(20)),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const ArrowBack(),
                    const YBox(50),
                    AuthTextSubTitle(
                      title: "Verify OTP",
                      useRichText: true,
                      subtitle: "Please enter the 6-digit code sent to ",
                      coloredText: context.read<AuthUserVM>().verifyMethod,
                      colorTextColor: AppColors.baseBlack,
                      colortextFontWeight: FontWeight.w600,
                    ),
                    const YBox(30),
                    Container(
                      alignment: Alignment.center,
                      child: Pinput(
                        defaultPinTheme: PinInputTheme.changeDefaultPinTheme(),
                        followingPinTheme: PinInputTheme.changePinTheme(),
                        focusedPinTheme: PinInputTheme.changeFocusPinTheme(),
                        submittedPinTheme: PinInputTheme.changePinTheme(),
                        length: 6,
                        controller: vm.otpC,
                        focusNode: pinFocusNode,
                        showCursor: true,
                        onCompleted: (pin) {
                          _verifyOtp();
                        },
                      ),
                    ),
                    const YBox(24),
                    ResendCode(
                      onResendCode: () {
                        vm.requestOtp().then((value) {
                          if (!value.success) {
                            FlushBarToast.fLSnackBar(
                              message: value.message.toString(),
                            );
                          }
                        });
                      },
                    ),
                    const Spacer(),
                    CustomBtn.solid(
                      borderRadius: BorderRadius.circular(Sizer.radius(20)),
                      onTap: () {
                        _verifyOtp();
                      },
                      online: vm.otpC.text.length == 6,
                      text: "Verify",
                    ),
                    const YBox(50),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  _verifyOtp() {
    pinFocusNode.unfocus();
    context.read<TrustedDeviceVM>().verifyAndSetTrustedDevice().then((value) {
      if (value.success) {
        _moveToDashboardScreen();
        _getWalletCredential();
      } else {
        _clearOtp();
        FlushBarToast.fLSnackBar(
          message: value.message.toString(),
        );
      }
    });
  }

  _clearOtp() {
    context.read<TrustedDeviceVM>().otpC.clear();
  }

  _getWalletCredential() {
    context.read<WalletVM>().getWallets();
    context.read<AuthUserVM>().getAuthUser();
    context.read<TransactionVM>().getTransactions();
  }

  _moveToDashboardScreen() {
    _clearOtp();
    Navigator.pushNamed(
      context,
      RoutePath.successConfirmScreen,
      arguments: SuccessConfirmArg(
        title: "Trusted Device Set Successfully",
        btnText: "Home",
        btnTap: () {
          Navigator.pushReplacementNamed(context, RoutePath.dashboardNav);
        },
      ),
    );
  }
}
