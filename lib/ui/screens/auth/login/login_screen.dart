import 'package:korrency/core/core.dart';
import 'package:korrency/session_manager.dart';
import 'package:korrency/ui/components/components.dart';
import 'package:local_session_timeout/local_session_timeout.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen>
    with TickerProviderStateMixin {
  late AnimationController _customController;
  late Animation<double> _customAnimation;

  final emailC = TextEditingController();
  final passwordC = TextEditingController();
  final _emailFocusNode = FocusNode();
  final _passwordFocusNode = FocusNode();

  @override
  void initState() {
    super.initState();

    _customController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _customAnimation = CurvedAnimation(
      parent: _customController,
      curve: Curves.easeInOut,
    );

    // Start form animation after a delay
    Future.delayed(const Duration(milliseconds: 300), () {
      _customController.forward();
    });

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _resetData();
    });
  }

  _resetData() {
    context.read<DashboardVM>().resetData();
    context.read<AuthUserVM>().resetData();
    context.read<TransactionVM>().resetData();
    context.read<WalletVM>().resetData();
    context.read<InactivityVM>()
      ..setPauseAction(false)
      ..setUserHasLoggedIn(false);
  }

  @override
  void dispose() {
    emailC.dispose();
    passwordC.dispose();
    _emailFocusNode.dispose();
    _passwordFocusNode.dispose();
    _customController.dispose();
    super.dispose();
  }

  bool get isValidEmail =>
      emailC.text.isNotEmpty &&
      emailC.text.contains("@") &&
      emailC.text.contains(".") &&
      emailC.text.split('.').last.isNotEmpty;

  bool get isFormValid => isValidEmail && passwordC.text.length > 7;

  @override
  Widget build(BuildContext context) {
    return Consumer<LoginVM>(
      builder: (context, vm, _) {
        return PopScope(
          canPop: false,
          child: BusyOverlay(
            show: vm.isBusy,
            child: Scaffold(
              backgroundColor: AppColors.bgWhite,
              body: SafeArea(
                bottom: false,
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: Sizer.width(24))
                      .copyWith(top: Sizer.height(20)),
                  child: FadeTransition(
                    opacity: _customAnimation,
                    child: SlideTransition(
                      position: _customAnimation.drive(
                        Tween<Offset>(
                          begin: const Offset(0, 0.2),
                          end: const Offset(0, 0),
                        ),
                      ),
                      child: ListView(
                        children: [
                          AuthHeader(
                            onTap: () {
                              Navigator.pop(context);
                            },
                          ),
                          const YBox(50),
                          const AuthTextSubTitle(
                            title: "Log in to Continue",
                            subtitle: "Enter your details in the fields below",
                          ),
                          const YBox(24),
                          CustomTextField(
                            labelText: "Email",
                            focusNode: _emailFocusNode,
                            showLabelHeader: true,
                            controller: emailC,
                            keyboardType: KeyboardType.email,
                            errorText: emailC.text.isNotEmpty && !isValidEmail
                                ? "Invalid Email"
                                : null,
                            borderRadius: Sizer.height(12),
                            prefixIcon: Padding(
                              padding: EdgeInsets.all(Sizer.radius(12)),
                              child: Icon(
                                Iconsax.sms,
                                color: AppColors.gray500,
                                size: Sizer.height(20),
                              ),
                            ),
                            onChanged: (_) => setState(() {}),
                            onSubmitted: (_) {
                              _emailFocusNode.unfocus();
                              FocusScope.of(context)
                                  .requestFocus(_passwordFocusNode);
                            },
                          ),
                          const YBox(24),
                          CustomTextField(
                            labelText: "Password",
                            focusNode: _passwordFocusNode,
                            showLabelHeader: true,
                            controller: passwordC,
                            isPassword: true,
                            borderRadius: Sizer.height(12),
                            prefixIcon: Padding(
                              padding: EdgeInsets.all(Sizer.radius(12)),
                              child: SvgPicture.asset(
                                AppSvgs.passwordCheck,
                              ),
                            ),
                            onChanged: (_) => vm.reBuildUI(),
                            onSubmitted: (_) {
                              _passwordFocusNode.unfocus();
                              // Implement login logic here
                              _login();
                            },
                          ),
                          const YBox(25),
                          const ForgotPasswordBtn(),
                          const YBox(25),
                          // ValidationItemWidget(
                          //   label: "Remeber my account",
                          //   isValid: vm.rememberMe,
                          //   onTap: () {
                          //     vm.setRememberMe(
                          //       !vm.rememberMe,
                          //     );
                          //   },
                          // ),
                          const YBox(140),
                          CustomBtn.withChild(
                            onTap: () {
                              _login();
                            },
                            online: isFormValid,
                            borderRadius:
                                BorderRadius.circular(Sizer.radius(20)),
                            child: ContinueText(
                              isOnline: isFormValid,
                            ),
                          ),
                          const YBox(30),
                          InkWell(
                            onTap: () {
                              Navigator.pushNamed(
                                  context, RoutePath.createAcctScreen);
                            },
                            child: RichText(
                                textAlign: TextAlign.center,
                                text: TextSpan(
                                  text: "Don’t have an account yet? ",
                                  style: AppTypography.text16.copyWith(
                                    color: AppColors.gray500,
                                    fontFamily: AppFont.outfit.family,
                                    height: 2,
                                  ),
                                  children: [
                                    TextSpan(
                                      text: "Create Account",
                                      style: AppTypography.text16.copyWith(
                                        color: AppColors.primaryBlue90,
                                        fontFamily: AppFont.outfit.family,
                                        height: 1.2,
                                      ),
                                    ),
                                  ],
                                )),
                          ),
                          const YBox(30),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  _login() async {
    final loginVm = context.read<LoginVM>();
    final r = await loginVm.login(
      email: emailC.text,
      password: passwordC.text,
    );

    handleApiResponse(
      response: r,
      showSuccessToast: false,
      onSuccess: () {
        sessionStateStream.add(SessionState.startListening);
        Navigator.pushReplacementNamed(context, RoutePath.dashboardNav);
      },
    );
  }
}
