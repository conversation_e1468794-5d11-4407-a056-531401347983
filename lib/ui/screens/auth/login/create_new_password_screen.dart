// ignore_for_file: use_build_context_synchronously

import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class CreateNewPasswordScreen extends StatefulWidget {
  const CreateNewPasswordScreen({super.key});

  @override
  State<CreateNewPasswordScreen> createState() =>
      _CreateNewPasswordScreenState();
}

class _CreateNewPasswordScreenState extends State<CreateNewPasswordScreen>
    with TickerProviderStateMixin {
  late AnimationController _customController;
  late Animation<double> _customAnimation;

  final FocusNode _passFocusNode = FocusNode();
  final FocusNode _confirmPassFocusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    _customController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    _customAnimation = CurvedAnimation(
      parent: _customController,
      curve: Curves.easeInOut,
    );

    // Start form animation after a delay
    Future.delayed(const Duration(milliseconds: 300), () {
      _customController.forward();
    });
    KeyboardOverlay.addRemoveFocusNode(context, _passFocusNode);
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<PasskeyResetVM>(
      builder: (context, vm, _) {
        return BusyOverlay(
          show: vm.isBusy,
          child: Scaffold(
            appBar: NewCustomAppbar(
              showHeaderTitle: true,
              headerText: 'New Password',
              onBackBtnTap: () {
                vm.clearData();
                Navigator.pop(context);
              },
            ),
            body: FadeTransition(
              opacity: _customAnimation,
              child: SlideTransition(
                position: _customAnimation.drive(
                  Tween<Offset>(
                    begin: const Offset(0, 0.2),
                    end: const Offset(0, 0),
                  ),
                ),
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: Sizer.width(24)),
                  child: Column(
                    children: [
                      Expanded(
                        child: SingleChildScrollView(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                "Please set a new password for your account",
                                style: AppTypography.text15
                                    .copyWith(color: AppColors.gray93),
                              ),
                              const YBox(24),
                              CustomTextField(
                                labelText: "Password",
                                showLabelHeader: true,
                                controller: vm.passwordC,
                                focusNode: _passFocusNode,
                                isPassword: true,
                                borderRadius: Sizer.height(12),
                                prefixIcon: Padding(
                                  padding: EdgeInsets.all(Sizer.radius(12)),
                                  child: SvgPicture.asset(
                                    AppSvgs.passwordCheck,
                                  ),
                                ),
                                onChanged: (val) {
                                  vm.validatePassword(vm.passwordC.text);
                                  vm.passwordMatch(vm.passwordC.text,
                                      vm.passwordConfirmC.text);
                                },
                                onSubmitted: (_) {
                                  _passFocusNode.unfocus();
                                  FocusScope.of(context)
                                      .requestFocus(_confirmPassFocusNode);
                                },
                              ),
                              // const YBox(8),
                              // Column(children: [
                              //   ValidationItemWidget(
                              //     label: "At least 8 characters long",
                              //     isValid: vm.validatorStatus.hasAtleast8Character,
                              //   ),
                              //   const YBox(8),
                              //   ValidationItemWidget(
                              //     label: "At least 1 Upper and 1 Lower case",
                              //     isValid: vm.validatorStatus.containsUpperCase,
                              //   ),
                              //   const YBox(8),
                              //   ValidationItemWidget(
                              //     label: "At least 1 number",
                              //     isValid: vm.validatorStatus.containsANumber,
                              //   ),
                              //   const YBox(8),
                              //   ValidationItemWidget(
                              //     label: "At least 1 special character @ # \$ %r",
                              //     isValid:
                              //         vm.validatorStatus.containsSpecialCharacter,
                              //   ),
                              // ]),
                              const YBox(24),
                              CustomTextField(
                                labelText: "Confirm Password",
                                showLabelHeader: true,
                                borderRadius: Sizer.height(12),
                                controller: vm.passwordConfirmC,
                                isPassword: true,
                                isConfirmPassword: true,
                                errorText: vm.passWordDontMatch
                                    ? 'Passwords do not match'
                                    : null,
                                prefixIcon: Padding(
                                  padding: EdgeInsets.all(Sizer.radius(12)),
                                  child: SvgPicture.asset(
                                    AppSvgs.passwordCheck,
                                  ),
                                ),
                                onChanged: (val) => vm.passwordMatch(
                                    vm.passwordC.text,
                                    vm.passwordConfirmC.text),
                                focusNode: _confirmPassFocusNode,
                                onSubmitted: (_) {
                                  _confirmPassFocusNode.unfocus();
                                },
                              ),
                              const YBox(20),
                              Column(children: [
                                ValidationItemWidget(
                                  label: "Must be up to 8 characters",
                                  isValid:
                                      vm.validatorStatus.hasAtleast8Character,
                                ),
                                const YBox(8),
                                ValidationItemWidget(
                                  label: "Must contain at least 1 number",
                                  isValid: vm.validatorStatus.containsANumber,
                                ),
                                const YBox(8),
                                ValidationItemWidget(
                                  label:
                                      "Must contain at least 1 uppercase letter",
                                  isValid: vm.validatorStatus.containsUpperCase,
                                ),
                                const YBox(8),
                                ValidationItemWidget(
                                  label:
                                      "Must contain at least 1 lowercase letter",
                                  isValid: vm.validatorStatus.containsLowerCase,
                                ),
                                const YBox(8),
                                ValidationItemWidget(
                                  label:
                                      "Must contain at least 1 special character",
                                  isValid: vm
                                      .validatorStatus.containsSpecialCharacter,
                                ),
                              ]),
                              const YBox(120),
                            ],
                          ),
                        ),
                      ),
                      CustomBtn.withChild(
                        onTap: () {
                          FocusScope.of(context).unfocus();
                          _saveChanges();
                        },
                        online: vm.enableSavePasswordBtn(),
                        borderRadius: BorderRadius.circular(Sizer.radius(20)),
                        child:
                            ContinueText(isOnline: vm.enableSavePasswordBtn()),
                      ),
                      const YBox(30),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  _saveChanges() {
    context.read<PasskeyResetVM>().resetPassword().then((value) {
      if (value.success) {
        BsWrapper.bottomSheet(
            canDismiss: false,
            context: context,
            widget: PasswordResetSuccessModal());
      } else {
        FlushBarToast.fLSnackBar(
          message: value.message.toString(),
        );
      }
    });
  }
}
