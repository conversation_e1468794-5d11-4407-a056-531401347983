import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';
import 'package:pinput/pinput.dart';

class PasswordResetScreen extends StatefulWidget {
  const PasswordResetScreen({super.key});

  @override
  State<PasswordResetScreen> createState() => _PasswordResetScreenState();
}

class _PasswordResetScreenState extends State<PasswordResetScreen> {
  FocusNode pinFocusNode = FocusNode();

  @override
  void initState() {
    KeyboardOverlay.addRemoveFocusNode(context, pinFocusNode);
    pinFocusNode.requestFocus();
    super.initState();
  }

  @override
  void dispose() {
    pinFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<PasskeyResetVM>(
      builder: (context, vm, _) {
        return BusyOverlay(
          show: vm.isBusy,
          child: Scaffold(
            appBar: NewCustomAppbar(
              showHeaderTitle: true,
              headerText: "Forgot Password",
              onBackBtnTap: () {
                vm.clearData();
                Navigator.pop(context);
              },
            ),
            body: Container(
              padding: EdgeInsets.symmetric(horizontal: Sizer.width(24)),
              child: Column(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          "Please enter the 6-digit code sent to ",
                          style: AppTypography.text16
                              .copyWith(color: AppColors.gray93),
                        ),
                        Text(
                          vm.emailC.text,
                          style: AppTypography.text16.medium
                              .copyWith(color: AppColors.primaryBlue90),
                        ),
                        const YBox(43),
                        Center(
                          child: Pinput(
                              defaultPinTheme:
                                  PinInputTheme.changeDefaultPinTheme(),
                              followingPinTheme: PinInputTheme.changePinTheme(),
                              focusedPinTheme:
                                  PinInputTheme.changeFocusPinTheme(),
                              submittedPinTheme: PinInputTheme.changePinTheme(),
                              length: 6,
                              controller: vm.otpC,
                              focusNode: pinFocusNode,
                              showCursor: true,
                              onChanged: (value) => vm.reBuildUI(),
                              onCompleted: (pin) {
                                pinFocusNode.unfocus();
                                _moveToEmailAndPasswordScreen();
                              }),
                        ),
                        const YBox(24),
                        ResendCode(
                          onResendCode: () {
                            _requestForgotPasswordOtp();
                          },
                        ),
                      ],
                    ),
                  ),
                  CustomBtn.withChild(
                    onTap: () {
                      pinFocusNode.unfocus();
                      _moveToEmailAndPasswordScreen();
                    },
                    online: vm.optBtnsValid(),
                    borderRadius: BorderRadius.circular(Sizer.radius(20)),
                    child: ContinueText(isOnline: vm.optBtnsValid()),
                  ),
                  const YBox(50),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  _requestForgotPasswordOtp() async {
    pinFocusNode.unfocus();
    context.read<PasskeyResetVM>().forgotPasswordOtpRequest();
  }

  _moveToEmailAndPasswordScreen() {
    pinFocusNode.unfocus();
    Navigator.pushReplacementNamed(context, RoutePath.createNewPasswordScreen);
  }
}
