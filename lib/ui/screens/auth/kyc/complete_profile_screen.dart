// ignore_for_file: use_build_context_synchronously

import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class CompleteProfileScreen extends StatefulWidget {
  const CompleteProfileScreen({super.key});

  @override
  State<CompleteProfileScreen> createState() => _CompleteProfileScreenState();
}

class _CompleteProfileScreenState extends State<CompleteProfileScreen> {
  final _genderC = TextEditingController();
  final _occupationC = TextEditingController();
  final _addressC = TextEditingController();
  final _stateC = TextEditingController();
  final _cityC = TextEditingController();
  final _postalCodeC = TextEditingController();

  OccupationData? selectedOccupation;
  bool _showListSelector = false;

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<KycVM>().getOccupationData();
    });
  }

  @override
  void dispose() {
    _genderC.dispose();
    _occupationC.dispose();
    _addressC.dispose();
    _stateC.dispose();
    _cityC.dispose();
    super.dispose();
  }

  bool get btnIsActive =>
      _genderC.text.isNotEmpty &&
      _occupationC.text.isNotEmpty &&
      _addressC.text.isNotEmpty &&
      _stateC.text.isNotEmpty &&
      _cityC.text.isNotEmpty &&
      _postalCodeC.text.isNotEmpty;

  @override
  Widget build(BuildContext context) {
    final kycVm = context.watch<KycVM>();
    final authVm = context.watch<AuthUserVM>();
    return BusyOverlay(
      show: kycVm.isBusy,
      child: Scaffold(
        backgroundColor: AppColors.bgWhite,
        appBar: NewCustomAppbar(
          showHeaderTitle: true,
          headerText: 'Complete your profile',
        ),
        body: ListView(
          children: [
            Padding(
              padding: EdgeInsets.symmetric(horizontal: Sizer.width(24)),
              child: Text(
                "Let’s get to know more about you",
                style: AppTypography.text15.copyWith(color: AppColors.gray93),
              ),
            ),
            const YBox(40),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: Sizer.width(16)),
              child: CustomTextField(
                labelText: "Gender",
                hintText: "Select your gender",
                showLabelHeader: true,
                isReadOnly: true,
                controller: _genderC,
                borderRadius: Sizer.height(12),
                showSuffixIcon: true,
                onTap: () async {
                  final res = await BsWrapper.bottomSheet(
                    context: context,
                    widget: SelectGenderModal(
                      selectedGender: _genderC.text.trim(),
                    ),
                  );

                  if (res is String) {
                    _genderC.text = res;
                    setState(() {});
                  }
                },
              ),
            ),
            const YBox(20),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: Sizer.width(16)),
              child: Column(
                children: [
                  CustomTextField(
                    labelText: "Occupation",
                    hintText: "Select your occupation",
                    isReadOnly: true,
                    showLabelHeader: true,
                    controller: _occupationC,
                    borderRadius: Sizer.height(12),
                    showSuffixIcon: true,
                    onSubmitted: (p0) {},
                    onTap: () {
                      _showListSelector = !_showListSelector;
                      setState(() {});
                    },
                  ),
                  AnimatedSize(
                    duration: const Duration(milliseconds: 500),
                    child: !_showListSelector
                        ? SizedBox.shrink()
                        : CustomListSelector<OccupationData>(
                            items: kycVm.occupationData,
                            displayText: (occupation) => occupation.name ?? '',
                            selectedValue: selectedOccupation,
                            onChanged: (occupation) {
                              selectedOccupation = occupation;
                              _occupationC.text = occupation?.name ?? '';
                              _showListSelector = false;
                              setState(() {});
                            },
                          ),
                  ),
                ],
              ),
            ),
            const YBox(20),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: Sizer.width(24)),
              child: CustomTextField(
                labelText: "Country of Residence",
                showLabelHeader: true,
                isReadOnly: true,
                borderRadius: Sizer.height(12),
                prefixIcon: Container(
                  padding: EdgeInsets.all(Sizer.radius(12)),
                  child: SvgPicture.asset(
                    CountryModel.getCountryByName(authVm.user?.country ?? "")
                            ?.flag ??
                        "",
                  ),
                ),
                controller: TextEditingController(
                    text: authVm.user?.country?.capitalize()),
                fillColor: AppColors.grayFA,
                suffixIcon: Padding(
                  padding: EdgeInsets.only(
                    right: Sizer.radius(10),
                  ),
                  child: Icon(
                    Iconsax.lock,
                    color: AppColors.gray500,
                    size: Sizer.height(16),
                  ),
                ),
                // controller: vm.emailController,
                onChanged: (val) {},
              ),
            ),
            const YBox(20),
            Consumer<AddressSuggestionVM>(builder: (context, vm, _) {
              return Column(
                children: [
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: Sizer.width(24)),
                    child: CustomTextField(
                        labelText: "Address",
                        showLabelHeader: true,
                        controller: _addressC,
                        hintText: "Enter your address",
                        borderRadius: Sizer.height(12),
                        prefixIcon: Icon(
                          Iconsax.location,
                          color: AppColors.gray500,
                          size: Sizer.height(16),
                        ),
                        onChanged: (val) async {
                          if (val.isNotEmpty) {
                            await context
                                .read<AddressSuggestionVM>()
                                .getPlacePredictions(val,
                                    countryValue: CountryModel.getCountryByName(
                                            authVm.user?.country ?? "")
                                        ?.countryValue);
                          } else {
                            vm.updatePredictions([]);
                          }
                        }),
                  ),
                  Builder(
                    builder: (context) {
                      if (vm.predictionViewState == ViewState.busy) {
                        return SizedBox(
                          height: Sizer.height(100),
                          child: const Center(
                            child: CircularProgressIndicator(),
                          ),
                        );
                      }
                      if (vm.predictionViewState == ViewState.error) {
                        return const SizedBox.shrink();
                      }

                      if (vm.predictions.isEmpty) {
                        return const SizedBox.shrink();
                      }

                      return Container(
                        decoration: BoxDecoration(
                          color: AppColors.white,
                          borderRadius: BorderRadius.circular(Sizer.radius(12)),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black12.withValues(alpha: 0.1),
                              blurRadius: 20,
                              offset: const Offset(0, 5),
                            ),
                          ],
                        ),
                        margin: EdgeInsets.symmetric(
                          horizontal: Sizer.width(16),
                        ),
                        padding: EdgeInsets.symmetric(
                          vertical: Sizer.height(20),
                          horizontal: Sizer.width(16),
                        ),
                        height: Sizer.height(250),
                        child: ListView.separated(
                          shrinkWrap: true,
                          separatorBuilder: (context, index) => YBox(8),
                          itemCount: vm.predictions.length,
                          itemBuilder: (context, index) {
                            printty(vm.predictions.length,
                                level: "from listView");
                            var prediction = vm.predictions[index];
                            return InkWell(
                              onTap: () async {
                                _addressC.text =
                                    vm.predictions[index].description ?? '';
                                if (prediction.placeId != null) {
                                  final placeDetails = await vm
                                      .getPlaceDetails(prediction.placeId!);
                                  printty("placeDetails: ${prediction.placeId}",
                                      level: "placeDetails");

                                  if (placeDetails.success &&
                                      placeDetails.data
                                          is List<AddressComponent>) {
                                    final addressC = placeDetails.data
                                        as List<AddressComponent>;
                                    _cityC.text = addressC
                                            .firstWhere(
                                              (component) =>
                                                  (component.types ?? [])
                                                      .contains('locality'),
                                              orElse: () => AddressComponent(
                                                  types: [],
                                                  longName: '',
                                                  shortName: ''),
                                            )
                                            .longName ??
                                        '';
                                    _stateC.text = addressC
                                            .firstWhere(
                                              (component) => (component.types ??
                                                      [])
                                                  .contains(
                                                      'administrative_area_level_1'),
                                              orElse: () => AddressComponent(
                                                  types: [],
                                                  longName: '',
                                                  shortName: ''),
                                            )
                                            .longName ??
                                        '';
                                    _postalCodeC.text = addressC
                                            .firstWhere(
                                              (component) =>
                                                  (component.types ?? [])
                                                      .contains('postal_code'),
                                              orElse: () => AddressComponent(
                                                  types: [],
                                                  longName: '',
                                                  shortName: ''),
                                            )
                                            .longName ??
                                        '';
                                  }
                                  vm.updatePredictions([]);
                                  setState(() {});
                                }
                                FocusScope.of(context).unfocus();
                              },
                              child: Padding(
                                padding: EdgeInsets.symmetric(
                                  vertical: Sizer.height(8),
                                ),
                                child: Text(
                                  prediction.description ?? "",
                                  style: AppTypography.text14.copyWith(
                                    color: AppColors.gray600,
                                  ),
                                ),
                              ),
                            );
                          },
                        ),
                      );
                    },
                  ),
                  YBox(20),
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: Sizer.width(24)),
                    child: CustomTextField(
                      labelText: "State",
                      showLabelHeader: true,
                      controller: _stateC,
                      hintText: "Enter your state",
                      borderRadius: Sizer.height(12),
                      prefixIcon: Padding(
                        padding: EdgeInsets.all(Sizer.radius(12)),
                        child: Icon(
                          Iconsax.location,
                          color: AppColors.gray500,
                          size: Sizer.height(16),
                        ),
                      ),
                      onChanged: (val) => setState(() {}),
                    ),
                  ),
                  YBox(20),
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: Sizer.width(24)),
                    child: CustomTextField(
                      labelText: "City",
                      showLabelHeader: true,
                      controller: _cityC,
                      hintText: "Enter your city",
                      borderRadius: Sizer.height(12),
                      prefixIcon: Padding(
                        padding: EdgeInsets.all(Sizer.radius(12)),
                        child: Icon(
                          Iconsax.location,
                          color: AppColors.gray500,
                          size: Sizer.height(16),
                        ),
                      ),
                      onChanged: (val) => setState(() {}),
                    ),
                  ),
                  YBox(20),
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: Sizer.width(24)),
                    child: CustomTextField(
                      labelText: "Postal Code",
                      showLabelHeader: true,
                      controller: _postalCodeC,
                      hintText: "Enter your postal code",
                      borderRadius: Sizer.height(12),
                      prefixIcon: Padding(
                        padding: EdgeInsets.all(Sizer.radius(12)),
                        child: Icon(
                          Iconsax.location,
                          color: AppColors.gray500,
                          size: Sizer.height(16),
                        ),
                      ),
                      onChanged: (val) => setState(() {}),
                    ),
                  ),
                ],
              );
            }),
            const YBox(100),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: Sizer.width(24)),
              child: CustomBtn.solid(
                onTap: () {
                  FocusScope.of(context).unfocus();
                  _submitProfile();
                },
                online: btnIsActive,
                borderRadius: BorderRadius.circular(Sizer.radius(20)),
                text: 'Save Details',
              ),
            ),
            const YBox(60),
          ],
        ),
      ),
    );
  }

  _submitProfile() async {
    final kycVm = context.read<KycVM>();

    final res = await kycVm.updateProfile(
      occupationId: selectedOccupation?.id ?? 0,
      address: _addressC.text,
      city: _cityC.text,
      state: _stateC.text,
      postalCode: _postalCodeC.text,
      gender: _genderC.text,
      kycStep: 2,
    );

    handleApiResponse(
      response: res,
      showSuccessToast: false,
      onSuccess: () {
        context.read<AuthUserVM>().setUser(res.data);
        Navigator.pop(context);
        BsWrapper.showCustomDialog(
          context,
          child: ConfirmationDialog(
            args: ConfirmationDialogArgs(
              title: "Profile Completed!",
              content: "Your Profile details has been saved successfully",
              btnText: "Done",
              onTap: () {
                final ctx = NavigatorKeys.appNavigatorKey.currentContext;
                Navigator.pop(ctx!);
              },
            ),
          ),
        );
      },
    );
  }
}
