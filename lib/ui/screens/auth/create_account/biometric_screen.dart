import 'package:korrency/core/core.dart';
import 'package:korrency/session_manager.dart';
import 'package:korrency/ui/components/components.dart';
import 'package:local_session_timeout/local_session_timeout.dart';

class BiometricScreen extends StatefulWidget {
  const BiometricScreen({super.key});

  @override
  State<BiometricScreen> createState() => _BiometricScreenState();
}

class _BiometricScreenState extends State<BiometricScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.bgWhite,
      body: SafeArea(
        bottom: false,
        child: Container(
          padding: EdgeInsets.symmetric(horizontal: Sizer.width(24))
              .copyWith(top: Sizer.height(20)),
          child: Column(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const AuthHeader(
                      showBackBtn: true,
                    ),
                    const YBox(50),
                    const AuthTextSubTitle(
                      title: "Enable Biometrics",
                      subtitle:
                          "For faster and more secure log in, enable your Fingerprint/Face ID.",
                    ),
                    const YBox(24),
                    Expanded(
                      child: Center(
                        child: Image.asset(
                          AppImages.fingerPrint,
                          height: Sizer.height(126),
                          width: Sizer.width(113),
                        ),
                      ),
                    )
                  ],
                ),
              ),
              CustomBtn.solid(
                onTap: () {
                  updateFingerPrint();
                },
                online: true,
                height: 65,
                text: "Enable Biometrics",
              ),
              const YBox(30),
              InkWell(
                onTap: () {
                  sessionStateStream.add(SessionState.startListening);
                  StorageService.removeStringItem(StorageKey.logoutCount);
                  Navigator.pushNamedAndRemoveUntil(
                      context, RoutePath.dashboardNav, (route) => false);
                },
                child: Text(
                  "Skip for Now",
                  style: AppTypography.text14.copyWith(
                    color: AppColors.gray500,
                    fontWeight: FontWeight.w400,
                    height: 2,
                  ),
                ),
              ),
              const YBox(39),
            ],
          ),
        ),
      ),
    );
  }

  updateFingerPrint() async {
    await BiometricService.authenticate().then((value) {
      if (value) {
        StorageService.storeBoolItem(StorageKey.fingerPrintIsEnabled, value);
        Navigator.pushNamed(
          context,
          RoutePath.successScreen,
          arguments: ConfirmationArg(
              title: "Welcome to Korrency",
              buttonText: 'Home',
              subtitle: const ConfirmationSubtitleText(
                startText: "Congratulations,",
                endText: " your account setup was successful.",
              ),
              onBtnTap: () {
                sessionStateStream.add(SessionState.startListening);
                Navigator.pushReplacementNamed(
                  context,
                  RoutePath.dashboardNav,
                );
              }),
        );
      } else {
        FlushBarToast.fLSnackBar(
          message: "Biometric Authentication Failed",
        );
      }
    });
  }
}
