// ignore_for_file: use_build_context_synchronously

import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';
import 'package:pinput/pinput.dart';

class VerifyEmailAddress extends StatefulWidget {
  const VerifyEmailAddress({super.key});

  @override
  State<VerifyEmailAddress> createState() => _VerifyEmailAddressState();
}

class _VerifyEmailAddressState extends State<VerifyEmailAddress> {
  final FocusNode pinFocusNode = FocusNode();
  bool isLoading = false;

  @override
  void initState() {
    super.initState();
    KeyboardOverlay.addRemoveFocusNode(context, pinFocusNode);
  }

  @override
  void dispose() {
    pinFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<OnBoardVM>(builder: (context, vm, _) {
      return BusyOverlay(
        show: vm.isBusy,
        child: Scaffold(
          backgroundColor: AppColors.bgWhite,
          appBar: NewCustomAppbar(
            showHeaderTitle: true,
            headerText: 'Verify your Email',
            onBackBtnTap: () {
              vm.clearEmailPasswordCredentials();
              Navigator.pop(context);
            },
          ),
          body: Container(
            padding: EdgeInsets.symmetric(horizontal: Sizer.width(24)),
            child: Column(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        "Please enter the 6-digit code sent to ",
                        style: AppTypography.text16
                            .copyWith(color: AppColors.gray93),
                      ),
                      Text(
                        vm.emailC.text,
                        style: AppTypography.text16
                            .copyWith(color: AppColors.primaryBlue),
                      ),
                      const YBox(46),
                      Center(
                        child: Pinput(
                          defaultPinTheme:
                              PinInputTheme.changeDefaultPinTheme(),
                          followingPinTheme: PinInputTheme.changePinTheme(),
                          focusedPinTheme: PinInputTheme.changeFocusPinTheme(),
                          submittedPinTheme: PinInputTheme.changePinTheme(),
                          length: 6,
                          controller: vm.emailOtp,
                          focusNode: pinFocusNode,
                          showCursor: true,
                          onChanged: (value) => vm..reBuildUI(),
                          onCompleted: (pin) {
                            _verifyOtp();
                          },
                        ),
                      ),
                      const YBox(24),
                      ResendCode(
                        onResendCode: () {
                          vm.requestOtp(otpType: OtpType.email);
                        },
                      ),
                    ],
                  ),
                ),
                CustomBtn.withChild(
                  onTap: () {
                    _verifyOtp();
                    // Navigator.pushNamed(
                    //     context, RoutePath.frequentRecipientCountryScreen);
                  },
                  online: vm.emailOtp.text.length == 6,
                  borderRadius: BorderRadius.circular(Sizer.radius(20)),
                  child: ContinueText(isOnline: vm.emailOtp.text.length == 6),
                ),
                const YBox(50),
              ],
            ),
          ),
        ),
      );
    });
  }

  _verifyOtp() {
    pinFocusNode.unfocus();
    context.read<OnBoardVM>().verifyOtp(otpType: OtpType.email).then((value) {
      if (value.success) {
        _moveToCreatePassword();
      } else {
        FlushBarToast.fLSnackBar(
          message: value.message.toString(),
        );
        _clearOtp();
      }
    });
  }

  _clearOtp() {
    context.read<OnBoardVM>().emailOtp.clear();
  }

  _moveToCreatePassword() {
    _clearOtp();
    Navigator.pushReplacementNamed(context, RoutePath.createPasswordScreen);
  }

  // void _verifyOtp() {
  //   setState(() {
  //     isLoading = true;
  //   });

  //   final onBoardVM = context.read<OnBoardVM>();

  //   onBoardVM.signUp().then((value) async {
  //     if (value.success) {
  //       // Track user registration completion
  //       try {
  //         await UnifiedAnalyticsManager.instance.trackCreateAccount(
  //           userId: value.data?['user']?['id']?.toString() ?? 'unknown',
  //           method: 'email',
  //           additionalParameters: {
  //             'platform': Platform.isIOS ? 'ios' : 'android',
  //             'registration_timestamp': DateTime.now().toIso8601String(),
  //             'has_referral_code': onBoardVM.referralC.text.trim().isNotEmpty,
  //           },
  //         );
  //       } catch (e) {
  //         printty('❌ Error tracking registration: $e');
  //       }

  //       // If sign up is successful, move to biometrics screen
  //       onBoardVM.removeDeepLinkValueFromStorage();
  //       _moveToCreatePassword();
  //     } else {
  //       // If sign up fails, clear OTP
  //       _displayErrorToast(value.message ?? '');
  //       _clearOtp();
  //     }
  //   });
  // }

  _displayErrorToast(String e) {
    FlushBarToast.fLSnackBar(
      message: e.toString(),
    );
  }

  // _clearOtp() {
  //   context.read<OnBoardVM>().emailOtp.clear();
  // }

  // _moveToFreqDesc() {
  //   _clearOtp();
  //   Navigator.pushReplacementNamed(
  //       context, RoutePath.frequentRecipientCountryScreen);
  // }

  // _moveToBiometricsScreen() {
  //   _clearOtp();
  //   Navigator.pushNamed(context, RoutePath.biometricScreen);
  // }
}
