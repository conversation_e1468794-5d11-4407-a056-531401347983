import 'package:dotted_border/dotted_border.dart';
import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class CreateBuyOfferPageview extends StatefulWidget {
  const CreateBuyOfferPageview({
    Key? key,
    this.onTap,
    required this.offerType,
  }) : super(key: key);

  final VoidCallback? onTap;
  final OfferType offerType;

  @override
  State<CreateBuyOfferPageview> createState() => _CreateBuyOfferPageviewState();
}

class _CreateBuyOfferPageviewState extends State<CreateBuyOfferPageview> {
  final FocusNode _fromFocusNode = FocusNode();
  final FocusNode _rateFocusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    KeyboardOverlay.addRemoveFocusNode(context, _fromFocusNode);
    _fromFocusNode.requestFocus();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<CreateOfferVM>().getFees(
          widget.offerType == OfferType.sell ? "sell_offer" : "buy_offer");
    });
  }

  unfocusAll() {
    _fromFocusNode.unfocus();
    _rateFocusNode.unfocus();
  }

  @override
  void dispose() {
    _fromFocusNode.dispose();
    _rateFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<CreateOfferVM>(builder: (context, vm, _) {
      return Container(
        padding: EdgeInsets.symmetric(
          horizontal: Sizer.width(24),
        ),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              AuthTextSubTitle(
                title:
                    "Create ${widget.offerType == OfferType.sell ? "Sell" : "Buy"} Offer",
                subtitle: "Complete the form to create your offer",
              ),
              const YBox(24),
              DottedBorder(
                dashPattern: const [8, 5],
                strokeWidth: 2,
                borderType: BorderType.RRect,
                radius: const Radius.circular(4),
                color: AppColors.dottedColor,
                padding: EdgeInsets.symmetric(
                    horizontal: Sizer.width(16), vertical: Sizer.height(20)),
                child: SizedBox(
                  width: Sizer.screenWidth,
                  child: Row(
                    children: [
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Amount I ${widget.offerType == OfferType.sell ? "have" : "need"}',
                            style: AppTypography.text12.copyWith(
                              color: AppColors.textGray,
                            ),
                          ),
                          const YBox(4),
                          Container(
                            height: Sizer.height(30),
                            color: AppColors.white,
                            width: Sizer.width(150),
                            child: ConvertTextfield(
                              hintText: "10,000.00",
                              focusNode: _fromFocusNode,
                              textAlign: TextAlign.start,
                              controller: vm.fromC,
                              onChanged: (val) => vm.reBuildUI(),
                            ),
                          ),
                        ],
                      ),
                      const Spacer(),
                      const CurrencyCards(
                        currencyCode: 'CAD',
                        showCurrencyCode: true,
                        showArrowIcon: false,
                        flagIconPath: AppImages.cadFlag,
                      )
                    ],
                  ),
                ),
              ),
              const YBox(20),
              DottedBorder(
                dashPattern: const [8, 5],
                strokeWidth: 2,
                borderType: BorderType.RRect,
                radius: const Radius.circular(4),
                color: AppColors.dottedColor,
                padding: EdgeInsets.symmetric(
                    horizontal: Sizer.width(16), vertical: Sizer.height(20)),
                child: SizedBox(
                  width: Sizer.screenWidth,
                  child: Row(
                    children: [
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Set your own rate',
                            style: AppTypography.text12.copyWith(
                              color: AppColors.textGray,
                            ),
                          ),
                          const YBox(4),
                          Container(
                            height: Sizer.height(30),
                            color: AppColors.white,
                            width: Sizer.width(150),
                            child: ConvertTextfield(
                              hintText: "1,350",
                              focusNode: _rateFocusNode,
                              textAlign: TextAlign.start,
                              controller: vm.rateC,
                              onChanged: (val) => vm.reBuildUI(),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
              // const YBox(4),
              // Row(
              //   mainAxisAlignment: MainAxisAlignment.end,
              //   children: [
              //     Text(
              //       'Current Marketplace Rates:',
              //       style: AppTypography.text10.copyWith(
              //         color: AppColors.gray000,
              //       ),
              //     ),
              //     const XBox(4),
              //     Text(
              //       'Highest 1,483 NGN',
              //       style: AppTypography.text10.copyWith(
              //         color: AppColors.baseGreen,
              //       ),
              //     ),
              //   ],
              // ),
              const YBox(26),
              ContainerWithBluewishBg(
                bgColor: AppColors.blue300,
                child: Row(
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Amount you’ll pay (includes ${((double.tryParse(vm.transactionFee?.fee ?? '0')) ?? 0) * 100}% fee)',
                          style: AppTypography.text12.copyWith(
                            color: AppColors.textGray,
                          ),
                        ),
                        const YBox(4),
                        if (widget.offerType == OfferType.sell)
                          Text(
                            '${AppUtils.formatAmountDoubleString(vm.sellAmountYouWillPay)} CAD ',
                            style: AppTypography.text20.copyWith(
                              fontWeight: FontWeight.w700,
                              color:
                                  _sellCheckAmountYouPayIsGreaterThanBalance()
                                      ? AppColors.red
                                      : AppColors.baseBlack,
                            ),
                          ),
                        if (widget.offerType == OfferType.buy)
                          Text(
                            '${AppUtils.formatAmountDoubleString(vm.buyAmountYouWillPay)} NGN ',
                            style: AppTypography.text20.copyWith(
                              fontWeight: FontWeight.w700,
                              color: _checkAmountYouPayIsGreaterThanBalance()
                                  ? AppColors.red
                                  : AppColors.baseBlack,
                            ),
                          ),
                      ],
                    ),
                    const Spacer(),
                    CurrencyCards(
                      currencyCode:
                          widget.offerType == OfferType.sell ? "CAD" : "NGN",
                      showCurrencyCode: true,
                      showArrowIcon: false,
                      bgColor: AppColors.transparent,
                      flagIconPath: widget.offerType == OfferType.sell
                          ? AppImages.cadFlag
                          : AppImages.ngnFlag,
                    )
                  ],
                ),
              ),
              const YBox(4),
              Builder(builder: (context) {
                var walletVM = context.watch<WalletVM>();
                return (widget.offerType == OfferType.sell)
                    ? Container(
                        alignment: Alignment.centerRight,
                        child: Text(
                          'Wallet Balance: ${AppUtils.formatAmountDoubleString(walletVM.cadWallet?.balance ?? "0")} CAD',
                          style: AppTypography.text12.copyWith(
                            color: AppColors.textBlue400,
                          ),
                        ),
                      )
                    : Container(
                        alignment: Alignment.centerRight,
                        child: Text(
                          'Wallet Balance: ${AppUtils.formatAmountDoubleString(walletVM.nairaWallet?.balance ?? "0")} NGN',
                          style: AppTypography.text12.copyWith(
                            color: AppColors.textBlue400,
                          ),
                        ),
                      );
              }),
              const YBox(150),
              CustomBtn.solid(
                onTap: widget.onTap,
                online: _btnIsActive(),
                text: "Continue",
              ),
              const YBox(150),
            ],
          ),
        ),
      );
    });
  }

  _btnIsActive() {
    var vm = context.read<CreateOfferVM>();
    if (widget.offerType == OfferType.sell) {
      return !_sellCheckAmountYouPayIsGreaterThanBalance() &&
          vm.fromC.text.isNotEmpty &&
          vm.rateC.text.isNotEmpty;
    } else {
      return !_checkAmountYouPayIsGreaterThanBalance() &&
          vm.fromC.text.isNotEmpty &&
          vm.rateC.text.isNotEmpty;
    }
  }

  bool _checkAmountYouPayIsGreaterThanBalance() {
    var balance = context.read<WalletVM>().nairaWallet?.balance;
    var amountYouPay = context.read<CreateOfferVM>().buyAmountYouWillPay;
    if (double.parse(amountYouPay) > double.parse(balance ?? "0")) {
      return true;
    }
    return false;
  }

  bool _sellCheckAmountYouPayIsGreaterThanBalance() {
    var balance = context.read<WalletVM>().cadWallet?.balance;
    var amountYouPay = context.read<CreateOfferVM>().sellAmountYouWillPay;
    if (double.parse(amountYouPay) > double.parse(balance ?? "0")) {
      return true;
    }
    return false;
  }
}
