import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';
import 'package:korrency/ui/screens/auth/kyc/setup_pin_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  @override
  void initState() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      MixpanelService().trackScreen("Home Page Viewed");
      _init();
    });
    super.initState();
  }

  _init() {
    final authUserVM = context.read<AuthUserVM>();

    authUserVM.getAuthUser().then((value) {
      if (value.success) {
        _getWalletCredential();
        _showSetups();

        _setTransactionPin();
        context.read<NotificationVM>().getNotifications();
      }
    });
  }

  bool pushUserToBiometricScreen() {
    int count = 0;
    bool isBiometricEnabled = false;
    StorageService.getString(StorageKey.logoutCount).then((value) {
      count = int.tryParse(value ?? "0") ?? 0;
    });

    StorageService.getBoolItem(StorageKey.fingerPrintIsEnabled).then((value) {
      isBiometricEnabled = value ?? false;
    });

    printty('count: $count, isBiometricEnabled: $isBiometricEnabled');

    if (count >= 3 && !isBiometricEnabled) {
      return true;
    }
    return false;
  }

  _setTransactionPin() {
    final authUserVM = context.read<AuthUserVM>();
    // Display Security Question
    // if (authUserVM.secQuestCheck) {
    //   Navigator.pushNamed(
    //     context,
    //     RoutePath.securityQuestionScreen,
    //   );
    // }
    if (authUserVM.transactionPinCheck) {
      BsWrapper.bottomSheet(
        context: context,
        canDismiss: false,
        widget: const SetupPinScreen(),
      );
    }
  }

  _showSetups() {
    final authUserVM = context.read<AuthUserVM>();

    if (!authUserVM.isTrustedDevice) {
      Future.delayed(const Duration(seconds: 3));
      return BsWrapper.bottomSheet(
        canDismiss: false,
        context: context,
        widget: const TrustedDeviceSheet(),
      );
    }

    if (authUserVM.user?.frequentDestinationCurrency == null) {
      Future.delayed(const Duration(seconds: 3));
      return Navigator.pushNamed(
        context,
        RoutePath.frequentRecipientCountryScreen,
        arguments: true,
      );
    }

    // if (authUserVM.kycVerifiedCheck) {
    //   Future.delayed(const Duration(seconds: 3));
    //   BsWrapper.bottomSheet(
    //     canDismiss: false,
    //     context: context,
    //     widget: const CompleteKycScreen(),
    //   );
    // }

    // if (authUserVM.openOccupationModal) {
    //   Future.delayed(const Duration(seconds: 3));
    //   BsWrapper.bottomSheet(
    //     canDismiss: false,
    //     context: context,
    //     widget: const UpdateAvailableScreen(),
    //   );
    //   return;
    // }

    if (pushUserToBiometricScreen() && authUserVM.userIsVerified) {
      Future.delayed(const Duration(seconds: 3));
      Navigator.pushNamed(context, RoutePath.biometricScreen);
      return;
    }
  }

  _getWalletCredential() {
    context.read<WalletVM>().getWallets();
    context.read<CurrencyVM>().getCurrencies();
    context.read<TransactionVM>().getTransactions();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AuthUserVM>(builder: (context, vm, _) {
      // printty("User cred ${vm.user?.frequentDestinationCurrency}");
      return SizedBox(
        width: Sizer.screenWidth,
        height: Sizer.screenHeight,
        child: BusyOverlay(
          show: vm.isBusy && !vm.refresh,
          child: Scaffold(
            body: Container(
              width: Sizer.screenWidth,
              height: Sizer.screenHeight,
              decoration: const BoxDecoration(
                image: DecorationImage(
                  image: AssetImage(AppImages.pattern),
                  fit: BoxFit.cover,
                ),
              ),
              child: SafeArea(
                  child: Column(
                children: [
                  Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: Sizer.width(24),
                    ).copyWith(
                      top: Sizer.height(20),
                    ),
                    child: Column(
                      children: [
                        Consumer<AuthUserVM>(builder: (context, vm, _) {
                          printty(vm.nameInitals, level: "nameInitals");
                          return Row(
                            children: [
                              InkWell(
                                onTap: () {
                                  Navigator.pushNamed(
                                      context, RoutePath.accountSettingScreen);
                                },
                                child: Container(
                                  height: Sizer.height(36),
                                  width: Sizer.width(36),
                                  decoration: BoxDecoration(
                                    color: AppColors.white,
                                    borderRadius: BorderRadius.circular(100),
                                  ),
                                  child: cacheNetWorkImage(
                                    vm.user?.avatarUrl ?? "",
                                    fit: BoxFit.cover,
                                  ),
                                ),
                              ),
                              const XBox(10),
                              RichText(
                                text: TextSpan(
                                  children: [
                                    TextSpan(
                                      text: "Hello ",
                                      style: AppTypography.text14.copyWith(
                                        color: AppColors.bgWhite,
                                      ),
                                    ),
                                    TextSpan(
                                      text: vm.user?.firstName ??
                                          vm.user?.firstName,
                                      style: AppTypography.text14.copyWith(
                                        color: AppColors.bgWhite,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              const Spacer(),
                              Consumer<NotificationVM>(
                                  builder: (context, notificationVM, _) {
                                return NotificationWidget(
                                  count: notificationVM.totalUnreadNotifications
                                      .toString(),
                                  onTap: () {
                                    Navigator.pushNamed(
                                        context, RoutePath.notifcationScreen);
                                  },
                                );
                              }),
                            ],
                          );
                        }),
                        const YBox(30),
                        Consumer<WalletVM>(builder: (context, walletVM, _) {
                          printty(
                              "Wallet List ${walletVM.activeWallet?.balance}");
                          return Row(
                            children: [
                              Column(
                                mainAxisSize: MainAxisSize.min,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    children: [
                                      Text(
                                        "Available Balance",
                                        style: AppTypography.text14.copyWith(
                                          color: AppColors.bgWhite,
                                        ),
                                      ),
                                      const XBox(5),
                                      InkWell(
                                        onTap: () {
                                          walletVM.setWalletVisibility();
                                        },
                                        child: Icon(
                                          walletVM.toggleWalletVisibility
                                              ? Iconsax.eye_slash5
                                              : Iconsax.eye4,
                                          color: AppColors.bgWhite,
                                          size: Sizer.radius(20),
                                        ),
                                      ),
                                    ],
                                  ),
                                  const YBox(8),
                                  walletVM.toggleWalletVisibility ||
                                          walletVM.isBusy
                                      ? Text(
                                          "*****",
                                          style: AppTypography.text28.copyWith(
                                            color: AppColors.bgWhite,
                                            height: 1.2,
                                            fontWeight: FontWeight.w600,
                                          ),
                                        )
                                      : RichText(
                                          text: TextSpan(
                                            children: [
                                              TextSpan(
                                                text: walletVM.homeBalance(
                                                    walletVM.activeWallet),
                                                style: AppTypography.text28
                                                    .copyWith(
                                                  color: AppColors.bgWhite,
                                                  fontWeight: FontWeight.w600,
                                                ),
                                              ),
                                              TextSpan(
                                                text: ".",
                                                style: AppTypography.text28
                                                    .copyWith(
                                                  color: AppColors.bgWhite,
                                                  fontWeight: FontWeight.w600,
                                                ),
                                              ),
                                              TextSpan(
                                                text: walletVM.decimalBalance(
                                                    walletVM.activeWallet),
                                                style: AppTypography.text16
                                                    .copyWith(
                                                  color: AppColors.bgWhite,
                                                  fontWeight: FontWeight.w600,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                ],
                              ),
                              const Spacer(),
                              InkWell(
                                onTap: () {
                                  BsWrapper.bottomSheet(
                                    context: context,
                                    widget: const AllWalletsSheet(),
                                  );
                                },
                                child: Container(
                                  padding: EdgeInsets.symmetric(
                                    horizontal: Sizer.width(16),
                                    vertical: Sizer.height(8),
                                  ),
                                  decoration: BoxDecoration(
                                    color: AppColors.bgWhite,
                                    borderRadius: BorderRadius.circular(
                                      Sizer.width(30),
                                    ),
                                  ),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      SvgPicture.network(
                                        walletVM.activeWallet?.currency?.flag ??
                                            AppUtils.kCadFlag,
                                        height: Sizer.height(14),
                                        width: Sizer.width(20),
                                      ),
                                      const XBox(5),
                                      Icon(
                                        Iconsax.arrow_down_1,
                                        color: AppColors.gray700,
                                        size: Sizer.radius(20),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ],
                          );
                        }),
                        const YBox(20),
                        Builder(
                          builder: (context) {
                            if (vm.user?.activeOffers == null ||
                                double.tryParse(vm.user?.activeOffers ?? "0") ==
                                    0) {
                              return const SizedBox();
                            }
                            return Row(
                              children: [
                                Text(
                                  "Active Offer:",
                                  style: AppTypography.text14.copyWith(
                                    color: AppColors.bgWhite,
                                  ),
                                ),
                                const XBox(6),
                                Text(
                                  "\$${vm.user?.activeOffers}",
                                  style: AppTypography.text14.copyWith(
                                    color: AppColors.bgWhite,
                                    fontWeight: FontWeight.w700,
                                  ),
                                ),
                              ],
                            );
                          },
                        ),
                        const YBox(18),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Consumer<WalletVM>(builder: (context, walletVM, _) {
                              return TransferCard(
                                svgPath: AppSvgs.send,
                                title: "Send Money",
                                onTap: () {
                                  if (vm.statusProcessing) {
                                    FlushBarToast.fLSnackBar(
                                      message:
                                          'Your ID verification is in progress',
                                      snackBarType: SnackBarType.success,
                                    );
                                    return;
                                  }
                                  if (vm.secQuestCheck) {
                                    Navigator.pushNamed(
                                      context,
                                      RoutePath.securityQuestionScreen,
                                    );
                                    return;
                                  }
                                  if (vm.transactionPinCheck) {
                                    BsWrapper.bottomSheet(
                                      context: context,
                                      canDismiss: false,
                                      widget: const SetupPinScreen(),
                                    );

                                    return;
                                  }

                                  /// if user has freq dest country set
                                  /// then use it as default
                                  final fromCurrencyCode =
                                      walletVM.activeWallet?.currency?.code ??
                                          '';
                                  final toCurrencyCode = vm.user
                                          ?.frequentDestinationCurrency?.code ??
                                      walletVM.inactiveWallet?.currency?.code ??
                                      '';

                                  Navigator.pushNamed(
                                    context,
                                    RoutePath.sendMoneyScreen,
                                    arguments: SendMoneyArg(
                                      fromCurrencyCode: fromCurrencyCode,
                                      toCurrencyCode:
                                          fromCurrencyCode.toLowerCase() !=
                                                  "cad"
                                              ? (walletVM.toCurrency?.code ??
                                                  CurrencyConstant.cadCurrency)
                                              : toCurrencyCode,
                                    ),
                                  );
                                },
                              );
                            }),
                            TransferCard(
                              svgPath: AppSvgs.received,
                              title: "Fund Wallet",
                              isOutlined: true,
                              onTap: () {
                                // if (vm.kycVerifiedCheck) {
                                //   BsWrapper.bottomSheet(
                                //     canDismiss: false,
                                //     context: context,
                                //     widget: const CompleteKycScreen(),
                                //   );

                                //   return;
                                // }
                                if (vm.awaitingReview) {
                                  FlushBarToast.fLSnackBar(
                                    message:
                                        'Your account is undergoing further KYC checks',
                                    snackBarType: SnackBarType.kyc,
                                  );

                                  return;
                                }
                                Navigator.pushNamed(
                                    context, RoutePath.preferredMehodScreen);
                              },
                            ),
                            InkWell(
                              onTap: () {
                                if (vm.awaitingReview) {
                                  FlushBarToast.fLSnackBar(
                                    message:
                                        'Your account is undergoing further KYC checks',
                                    snackBarType: SnackBarType.kyc,
                                  );
                                  return;
                                }

                                AppsFlyerService.instance.logEvent(
                                  "send_money",
                                  <String, dynamic>{
                                    'currency': 'CAD',
                                    'amount': '100',
                                  },
                                );

                                // BsWrapper.bottomSheet(
                                //   context: context,
                                //   color: AppColors.transparent,
                                //   widget: const OtherActionsSheet(),
                                // );
                              },
                              child: Container(
                                height: Sizer.height(24),
                                width: Sizer.width(24),
                                decoration: BoxDecoration(
                                  color: AppColors.bgWhite,
                                  borderRadius: BorderRadius.circular(
                                    Sizer.width(30),
                                  ),
                                ),
                                child: Center(
                                  child: Icon(
                                    Icons.more_horiz,
                                    color: AppColors.gray700,
                                    size: Sizer.radius(20),
                                  ),
                                ),
                              ),
                            )
                          ],
                        ),
                        const YBox(36),
                      ],
                    ),
                  ),
                  Expanded(
                    child: HomeLowerPart(
                      pullToRefresh: () {
                        if (!vm.isBusy) _init();
                      },
                    ),
                  ),
                ],
              )),
            ),
          ),
        ),
      );
    });
  }
}

class NotificationWidget extends StatelessWidget {
  const NotificationWidget({super.key, this.onTap, this.count = '0'});

  final VoidCallback? onTap;
  final String count;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: SizedBox(
        // color: AppColors.red,
        width: Sizer.height(25),
        height: Sizer.width(25),
        child: Stack(
          clipBehavior: Clip.none,
          alignment: Alignment.center,
          children: [
            const Icon(
              Iconsax.notification5,
              color: AppColors.bgWhite,
              size: 25,
            ),
            if ((int.tryParse(count) ?? 0) > 0)
              Positioned(
                right: -3,
                top: -5,
                child: Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: Sizer.width(3),
                    vertical: Sizer.height(1),
                  ),
                  decoration: BoxDecoration(
                    color: AppColors.iconBadge,
                    borderRadius: BorderRadius.circular(
                      Sizer.width(10),
                    ),
                  ),
                  child: Text(
                    count,
                    style: AppTypography.text10.copyWith(
                      color: AppColors.bgWhite,
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}


// rQ2dCnyNQ6NWHgzNryorpA