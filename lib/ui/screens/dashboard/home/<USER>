import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/common/reusable_swiper_card.dart';
import 'package:korrency/ui/components/components.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  int? _currentCurrencyId;
  int _focusedWalletIndex = 0; // Track the currently focused wallet
  Currency? focusWalletCurrency;

  @override
  void initState() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      MixpanelService().trackScreen("Home Page Viewed");
      _init();
    });
    super.initState();
  }

  _init() {
    final authUserVM = context.read<AuthUserVM>();

    authUserVM.getAuthUser().then((value) {
      if (value.success) {
        _getWalletCredential();
        _showSetups();

        _setTransactionPin();
        context.read<NotificationVM>().getNotifications();
      }
    });
  }

  bool pushUserToBiometricScreen() {
    int count = 0;
    bool isBiometricEnabled = false;
    StorageService.getString(StorageKey.logoutCount).then((value) {
      count = int.tryParse(value ?? "0") ?? 0;
    });

    StorageService.getBoolItem(StorageKey.fingerPrintIsEnabled).then((value) {
      isBiometricEnabled = value ?? false;
    });

    printty('count: $count, isBiometricEnabled: $isBiometricEnabled');

    if (count >= 3 && !isBiometricEnabled) {
      return true;
    }
    return false;
  }

  _setTransactionPin() {
    final authUserVM = context.read<AuthUserVM>();
    // Display Security Question
    // if (authUserVM.secQuestCheck) {
    //   Navigator.pushNamed(
    //     context,
    //     RoutePath.securityQuestionScreen,
    //   );
    // }
    if (authUserVM.transactionPinCheck) {
      return Navigator.pushNamed(context, RoutePath.setupPinScreen);
      // BsWrapper.bottomSheet(
      //   context: context,
      //   canDismiss: false,
      //   widget: const SetupPinScreen(),
      // );
    }
  }

  _showSetups() {
    final authUserVM = context.read<AuthUserVM>();

    if (!authUserVM.isTrustedDevice) {
      Future.delayed(const Duration(seconds: 3));
      return BsWrapper.bottomSheet(
        canDismiss: false,
        context: context,
        widget: const TrustedDeviceSheet(),
      );
    }

    if (authUserVM.user?.frequentDestinationCurrency == null) {
      Future.delayed(const Duration(seconds: 3));
      return Navigator.pushNamed(
        context,
        RoutePath.frequentRecipientCountryScreen,
        arguments: true,
      );
    }

    if ((authUserVM.user?.kycStep ?? 0) < 2) {
      Future.delayed(const Duration(seconds: 3));
      Navigator.pushNamed(context, RoutePath.completeProfileScreen);
    }

    if (pushUserToBiometricScreen() && authUserVM.userIsVerified) {
      Future.delayed(const Duration(seconds: 3));
      // Navigator.pushNamed(context, RoutePath.biometricScreen);
      BsWrapper.bottomSheet(
        context: context,
        widget: EnableBiometricsModal(),
      );
      return;
    }
  }

  _getWalletCredential() async {
    final walletVm = context.read<WalletVM>();
    final currencyVm = context.read<CurrencyVM>();
    final transactionVm = context.read<TransactionVM>();

    await Future.wait([
      currencyVm.getCurrencies(),
      walletVm.getWallets(),
      transactionVm.getTransactions(),
    ]);

    _getFreqBeneficiaries(walletVm.primaryWalletCurrecyId);
    await currencyVm.getRatesByCurrencyId(walletVm.primaryWalletCurrecyId);
  }

  // Get frequent beneficiaries
  _getFreqBeneficiaries(int currencyId) {
    // Only call if currency has changed
    if (_currentCurrencyId != currencyId) {
      _currentCurrencyId = currencyId;
      final beneficiaryVm = context.read<BeneficiaryVM>();

      beneficiaryVm.getFreqBeneficiaries(currencyId: currencyId);
    }
  }

  @override
  Widget build(BuildContext context) {
    final walletVm = context.watch<WalletVM>();
    final authVm = context.watch<AuthUserVM>();
    final beneficiaryVm = context.watch<BeneficiaryVM>();
    final transactionVm = context.watch<TransactionVM>();

    return SizedBox(
      width: Sizer.screenWidth,
      height: Sizer.screenHeight,
      child: BusyOverlay(
        show: authVm.isBusy,
        child: Scaffold(
          appBar: HomeHeader(),
          body: RefreshIndicator(
            onRefresh: () async {
              await _init();
            },
            child: ListView(
              padding: EdgeInsets.only(bottom: 80),
              children: [
                const YBox(32),
                if (walletVm.walletList.isNotEmpty)
                  Column(
                    children: [
                      Padding(
                        padding: EdgeInsets.symmetric(
                          horizontal: Sizer.width(24),
                        ),
                        child: Row(
                          children: [
                            InkWell(
                              onTap: () {
                                walletVm.setWalletVisibility();
                              },
                              child: Row(
                                children: [
                                  Text(
                                    "Wallet",
                                    style: AppTypography.text16.semiBold,
                                  ),
                                  XBox(8),
                                  SvgPicture.asset(
                                    walletVm.toggleWalletVisibility
                                        ? AppSvgs.eye
                                        : AppSvgs.eyeSlash,
                                  ),
                                ],
                              ),
                            ),
                            Spacer(),
                            InkWell(
                              onTap: () {
                                // BsWrapper.bottomSheet(
                                //   context: context,
                                //   widget: const AllWalletsSheet(),
                                // );
                                BsWrapper.bottomSheet(
                                  context: context,
                                  widget: const AddWalletPickerModal(),
                                );
                              },
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Icon(
                                    Icons.add,
                                    color: AppColors.blueD8,
                                    size: Sizer.radius(16),
                                  ),
                                  XBox(4),
                                  Text(
                                    "Add wallet",
                                    style: AppTypography.text12.medium.copyWith(
                                      color: AppColors.blueD8,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                      Container(
                        padding:
                            EdgeInsets.symmetric(horizontal: Sizer.width(46)),
                        height: Sizer.height(164 +
                            (walletVm.walletList.length > 1
                                ? (walletVm.walletList.length - 1) * 50 + 56
                                : 56)),
                        child: Center(
                          child: walletVm.isBusy
                              ? CupertinoActivityIndicator()
                              : ReusableSwiperCard(
                                  key: const ValueKey('wallet_swiper_card'),
                                  cards: _buildWalletCards(),
                                  initialIndex: _focusedWalletIndex,
                                  onSwipe: (originalIndex, isRightSwipe,
                                      cardWidget) {
                                    _focusedWalletIndex = originalIndex;

                                    final activeWallet =
                                        walletVm.walletList[originalIndex];
                                    walletVm.setActiveWallet(activeWallet);
                                    _getFreqBeneficiaries(
                                        activeWallet.currency?.id ?? 0);

                                    // get exchange rate
                                    context
                                        .read<CurrencyVM>()
                                        .getRatesByCurrencyId(
                                            activeWallet.currency?.id ?? 0);
                                  },
                                  onTap: (originalIndex, cardWidget) {
                                    _focusedWalletIndex = originalIndex;
                                    final activeWallet =
                                        walletVm.walletList[originalIndex];
                                    walletVm.setActiveWallet(activeWallet);
                                  },
                                  onPrimaryTap: (originalIndex, cardWidget) {
                                    final code = walletVm
                                        .walletList[originalIndex]
                                        .currency
                                        ?.code;
                                    printty("Code $code");

                                    navigateToWalletScreen(
                                      context: context,
                                      currencyCode: code ?? '',
                                      wallet:
                                          walletVm.walletList[originalIndex],
                                    );
                                  },
                                  cardHeight: Sizer.height(164),
                                  cardOffset: 50.0,
                                  animationDuration:
                                      const Duration(milliseconds: 300),
                                  enableSwipe: walletVm.walletList.length > 1,
                                  enableBackgroundTap: true,
                                  enablePrimaryTap: true,
                                  minDragDistance: 50.0,
                                  minVelocity: 100.0,
                                ),
                        ),
                      ),
                    ],
                  ),
                const YBox(20),
                AnimatedSize(
                  duration: const Duration(milliseconds: 300),
                  child: GetStartedWidget(),
                ),
                const YBox(20),
                Padding(
                  padding: EdgeInsets.symmetric(
                    horizontal: Sizer.width(24),
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        child: HomeTab(
                          isActive: true,
                          text: "Send",
                          onTap: () {
                            if (authVm.statusProcessing) {
                              return BsWrapper.showCustomDialog(
                                context,
                                child: DocumentPendingVerificationDialog(),
                              );
                            }

                            if (!authVm.userIsVerified) {
                              showWarningToast(
                                  'Complete your KYC to send money');
                              return null;
                            }

                            /// if user has freq dest country set
                            /// then use it as default
                            final fromCurrencyCode =
                                walletVm.activeWallet?.currency?.code ?? '';
                            final toCurrencyCode = authVm
                                    .user?.frequentDestinationCurrency?.code ??
                                walletVm.inactiveWallet?.currency?.code ??
                                '';

                            Navigator.pushNamed(
                              context,
                              RoutePath.sendMoneyScreen,
                              arguments: SendMoneyArg(
                                fromCurrencyCode: fromCurrencyCode,
                                toCurrencyCode:
                                    fromCurrencyCode.toLowerCase() != "cad"
                                        ? (walletVm.toCurrency?.code ??
                                            CurrencyConstant.cadCurrency)
                                        : toCurrencyCode,
                              ),
                            );

                            // BsWrapper.bottomSheet(
                            //   context: context,
                            //   widget: SendModal(),
                            // );
                          },
                        ),
                      ),
                      XBox(8),
                      Expanded(
                        child: HomeTab(
                          text: "Add Money",
                          onTap: () async {
                            final res = await BsWrapper.bottomSheet(
                              context: context,
                              widget: SelectWalletModal(),
                            );

                            if (res is String && res.isNotEmpty) {
                              if (context.mounted) {
                                BsWrapper.bottomSheet(
                                  context: context,
                                  widget: FundYourWalletModal(
                                    currencyCode: res,
                                  ),
                                );
                              }
                            }
                          },
                        ),
                      ),
                      XBox(8),
                      Expanded(
                        child: HomeTab(
                          text: "Convert",
                          onTap: () {
                            if (!authVm.userIsVerified) {
                              showWarningToast(
                                  'Complete your KYC to convert currency');
                              return;
                            }
                            Navigator.pushNamed(
                                context, RoutePath.convertCurrencyScreen);
                          },
                        ),
                      ),
                    ],
                  ),
                ),
                YBox(26),
                if (beneficiaryVm.homeFreqBeneficiaries.isNotEmpty)
                  Column(
                    children: [
                      Padding(
                        padding: EdgeInsets.symmetric(
                          horizontal: Sizer.width(24),
                        ),
                        child: ViewAllRowWidget(
                          leftTitle: "Quick Send",
                          onViewAll: () {
                            Navigator.pushNamed(
                              context,
                              RoutePath.quickSendScreen,
                            );
                          },
                        ),
                      ),
                      YBox(12),
                      LoadableContentBuilder(
                        isBusy: beneficiaryVm.isBusy,
                        loadingBuilder: (ctx) {
                          return Align(
                            alignment: Alignment.centerLeft,
                            child: SingleChildScrollView(
                              scrollDirection: Axis.horizontal,
                              child: Row(
                                children: List.generate(
                                  1,
                                  (i) => Padding(
                                    padding: EdgeInsets.only(
                                      left: i == 0 ? Sizer.width(24) : 0,
                                      right: Sizer.width(12),
                                    ),
                                    child: Skeletonizer(
                                      enabled: true,
                                      child: QuickSendWidget(
                                        beneficiaryName: "Beneficiary Name",
                                        currencyFlag: AppUtils.kCadFlag,
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          );
                        },
                        emptyBuilder: (ctx) {
                          return SizedBox.shrink();
                        },
                        contentBuilder: (context) {
                          return Align(
                            alignment: Alignment.centerLeft,
                            child: SingleChildScrollView(
                              scrollDirection: Axis.horizontal,
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.start,
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  SizedBox(width: Sizer.width(24)),
                                  ...List.generate(
                                      beneficiaryVm
                                          .homeFreqBeneficiaries.length, (i) {
                                    final beneficiary =
                                        beneficiaryVm.homeFreqBeneficiaries[i];
                                    return Padding(
                                      padding: EdgeInsets.only(
                                        right: Sizer.width(12),
                                      ),
                                      child: QuickSendWidget(
                                        beneficiaryName:
                                            beneficiary.accountName ?? "",
                                        currencyFlag:
                                            beneficiary.currency?.flag ?? "",
                                        onTap: () {
                                          final fromCurrencyCode = beneficiary
                                                  .latestTransaction
                                                  ?.currency
                                                  ?.code ??
                                              "";
                                          final toCurrencyCode = beneficiary
                                                  .latestTransaction
                                                  ?.receivedCurrency
                                                  ?.code ??
                                              "";
                                          printty(
                                              "fromCurrencyCode $fromCurrencyCode");
                                          printty(
                                              "toCurrencyCode $toCurrencyCode");
                                          Navigator.pushNamed(
                                            context,
                                            RoutePath.sendMoneyScreen,
                                            arguments: SendMoneyArg(
                                              fromCurrencyCode:
                                                  fromCurrencyCode,
                                              toCurrencyCode: toCurrencyCode,
                                              fromAmount: beneficiary
                                                      .latestTransaction
                                                      ?.amount ??
                                                  "",
                                              beneficiary: beneficiary,
                                            ),
                                          );
                                        },
                                      ),
                                    );
                                  }),
                                ],
                              ),
                            ),
                          );
                        },
                      ),
                    ],
                  ),
                YBox(26),
                Skeletonizer(
                  enabled: authVm.isBusy,
                  child: Skeleton.replace(
                    replacement: Padding(
                      padding:
                          EdgeInsets.symmetric(horizontal: Sizer.width(24)),
                      child: Bone(
                        height: 70,
                        width: double.infinity,
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: AnimatedExchangeRateWidget(
                      onTap: () {
                        Navigator.pushNamed(
                          context,
                          RoutePath.exchangeRateScreen,
                        );
                      },
                    ),
                  ),
                ),
                YBox(30),
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: Sizer.width(24)),
                  child: ViewAllRowWidget(
                    leftTitle: "Quick actions",
                  ),
                ),
                YBox(12),
                SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Row(
                    children: [
                      XBox(24),
                      QuickActionCard(
                        imgaeBg: AppImages.ratesAction,
                        svgIcon: AppSvgs.ratesAction,
                        title: "Rates",
                        subTitle: "Real-time rates to convert or send money",
                        iconPosition: IconPosition(
                          bottom: 6,
                          right: 0,
                        ),
                        onTap: () {
                          Navigator.pushNamed(
                              context, RoutePath.exchangeRateScreen);
                        },
                      ),
                      XBox(12),
                      QuickActionCard(
                        imgaeBg: AppImages.inviteAction,
                        svgIcon: AppSvgs.inviteAction,
                        title: "Invite & Earn",
                        subTitle: "Invite family and friends and earn \$15",
                        iconPosition: IconPosition(
                          bottom: 6,
                          right: 0,
                        ),
                        onTap: () {
                          Navigator.pushNamed(
                            context,
                            RoutePath.inviteAndEarnScreen,
                          );
                        },
                      ),
                      XBox(12),
                      QuickActionCard(
                        imgaeBg: AppImages.earningsAction,
                        svgIcon: AppSvgs.earningsAction,
                        title: "Earnings",
                        subTitle: "Real-time rates to convert or send money",
                        iconPosition: IconPosition(
                          bottom: 0,
                          right: 20,
                        ),
                        onTap: () {
                          Navigator.pushNamed(
                            context,
                            RoutePath.dashboardNav,
                            arguments: 3,
                          );
                        },
                      ),
                      XBox(12),
                      QuickActionCard(
                        imgaeBg: AppImages.convertAction,
                        svgIcon: AppSvgs.convertAction,
                        title: "Convert",
                        subTitle: "Real-time rates to convert or send money",
                        iconPosition: IconPosition(
                          bottom: 10,
                          right: 0,
                        ),
                        onTap: () {
                          Navigator.pushNamed(
                              context, RoutePath.convertCurrencyScreen);
                        },
                      ),
                      XBox(12),
                      QuickActionCard(
                        imgaeBg: AppImages.limitAction,
                        svgIcon: AppSvgs.limitAction,
                        title: "Account Limits",
                        subTitle:
                            "See your transaction limits for different countries",
                        iconSize: 70,
                        iconPosition: IconPosition(
                          bottom: 10,
                          right: 20,
                        ),
                        onTap: () {
                          Navigator.pushNamed(
                              context, RoutePath.accountLimitScreen,
                              arguments: walletVm.activeWallet?.currency);
                        },
                      ),
                      XBox(40),
                    ],
                  ),
                ),
                YBox(26),
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: Sizer.width(24)),
                  child: ViewAllRowWidget(
                    leftTitle: "Transactions",
                    onViewAll: transactionVm.recentTransactions.isEmpty
                        ? null
                        : () {
                            Navigator.pushNamed(context, RoutePath.dashboardNav,
                                arguments: 1);
                          },
                  ),
                ),
                YBox(26),
                Builder(builder: (context) {
                  final transactions = transactionVm.recentTransactions;
                  return LoadableContentBuilder(
                      isBusy: transactionVm.busy(TransactionState.all),
                      items: transactionVm.recentTransactions,
                      loadingBuilder: (ctx) {
                        return ListView.separated(
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          padding: EdgeInsets.symmetric(
                            horizontal: Sizer.width(24),
                          ),
                          itemCount: 3,
                          separatorBuilder: (_, __) => YBox(26),
                          itemBuilder: (ctx, i) {
                            return Skeletonizer(
                              enabled: true,
                              child: TransactionListTile(
                                title: "Transaction",
                                subTitle: "Transaction",
                                amount: "1000",
                                status: "Success",
                                category: "Transfer",
                                onTap: () {},
                              ),
                            );
                          },
                        );
                      },
                      emptyBuilder: (ctx) {
                        return SizedBox(
                          height: Sizer.height(300),
                          child: Center(
                            child: EmptyState(
                              title: "No transaction found",
                            ),
                          ),
                        );
                      },
                      contentBuilder: (context) {
                        return ListView.separated(
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          padding: EdgeInsets.symmetric(
                            horizontal: Sizer.width(24),
                          ),
                          itemCount: transactions.length,
                          separatorBuilder: (_, __) =>
                              HDivider(verticalPadding: 16),
                          itemBuilder: (ctx, i) {
                            return TransactionListTile(
                              title: transactions[i].title ?? "",
                              subTitle: transactions[i].subtitle ?? "",
                              amount:
                                  "${transactions[i].type == "credit" ? "+" : "-"} ${AppUtils.formatAmountDoubleString(transactions[i].amount ?? "0")}  ${transactions[i].currency?.code ?? ""}",
                              status: transactions[i].status ?? "",
                              category:
                                  transactions[i].category?.toLowerCase() ?? "",
                              onTap: () {
                                Navigator.of(context).pushNamed(
                                  RoutePath.transactionDetailsScreen,
                                  arguments: TransactionArg(
                                    transaction: transactions[i],
                                  ),
                                );
                              },
                            );
                          },
                        );
                      });
                })
              ],
            ),
          ),
        ),
      ),
    );
  }

  List<Widget> _buildWalletCards() {
    final walletVm = context.watch<WalletVM>();
    final List<Widget> cards = [];

    for (int i = 0; i < walletVm.walletList.length; i++) {
      final wallet = walletVm.walletList[i];
      final isPrimary = i == _focusedWalletIndex;

      cards.add(WalletCard(
        currency: isPrimary
            ? "${wallet.currency?.code ?? ""} Balance"
            : wallet.currency?.code ?? "",
        flag: wallet.currency?.flag ?? "",
        symbol: wallet.currency?.symbol ?? "",
        amount: walletVm.toggleWalletVisibility
            ? walletVm.homeBalance(wallet, false)
            : "********",
        amountFraction: walletVm.toggleWalletVisibility
            ? walletVm.decimalBalance(wallet)
            : "",
        isPrimary: isPrimary,
        bottomTitle: _getButtomText(wallet)["title"] ?? "",
        bottomSubTitle:
            AppUtils.maskEmail(_getButtomText(wallet)["subTitle"] ?? ""),
        textColor: (wallet.currency?.code?.toLowerCase().contains("usd") ??
                    false) ||
                (wallet.currency?.code?.toLowerCase().contains("kes") ?? false)
            ? AppColors.primaryBlue
            : AppColors.white,
        bgImage: getWalletBg(wallet.currency?.code ?? ""),
      ));
    }

    return cards;
  }

  Map<String, String> _getButtomText(Wallet wallet) {
    final code = wallet.currency?.code;
    switch (code) {
      case "NGN":
        return {
          "title": "Account Number",
          "subTitle": wallet.virtualAccounts?.isNotEmpty == true
              ? wallet.virtualAccounts?.first.accountNumber ?? ""
              : "",
        };
      case "CAD":
        return {
          "title": "Primary Interac",
          "subTitle": wallet.virtualAccounts?.isNotEmpty == true
              ? wallet.virtualAccounts?.first.accountNumber ?? ""
              : "",
        };
      case "KES":
        return {
          "title": "Primary Interac",
          "subTitle": wallet.virtualAccounts?.isNotEmpty == true
              ? wallet.virtualAccounts?.first.accountNumber ?? ""
              : "",
        };
      case "GBP":
        return {
          "title": "IBAN",
          "subTitle": wallet.virtualAccounts?.isNotEmpty == true
              ? wallet.virtualAccounts?.first.accountNumber ?? ""
              : "",
        };
      default:
        return {
          "title": "",
          "subTitle": "",
        };
    }
  }
}

// rQ2dCnyNQ6NWHgzNryorpA
