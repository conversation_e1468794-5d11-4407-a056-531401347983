import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class QuickSendScreen extends StatefulWidget {
  const QuickSendScreen({
    super.key,
  });

  @override
  State<QuickSendScreen> createState() => _QuickSendScreenState();
}

class _QuickSendScreenState extends State<QuickSendScreen> {
  final _searchC = TextEditingController();

  @override
  void initState() {
    super.initState();
    _searchC.addListener(_onSearchChanged);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _init();
    });
  }

  void _onSearchChanged() {
    final beneficiaryVm = context.read<BeneficiaryVM>();
    beneficiaryVm.searchHomeFreqBeneficiaries(_searchC.text);
  }

  _init() {
    final beneficiaryVm = context.read<BeneficiaryVM>();
    beneficiaryVm.getFreqBeneficiaries();
  }

  @override
  void dispose() {
    _searchC.removeListener(_onSearchChanged);
    _searchC.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final beneVm = context.watch<BeneficiaryVM>();

    return BusyOverlay(
      show: beneVm.isBusy,
      child: Scaffold(
        backgroundColor: AppColors.bgWhite,
        appBar: NewCustomAppbar(
          showHeaderTitle: true,
          headerText: "Quick Send to",
        ),
        body: Container(
          padding: EdgeInsets.symmetric(horizontal: Sizer.width(24)),
          child: Column(
            children: [
              const YBox(20),
              CustomTextField(
                controller: _searchC,
                prefixIcon: Padding(
                  padding: EdgeInsets.all(Sizer.radius(12)),
                  child: SvgPicture.asset(AppSvgs.search),
                ),
                hintText: "Search Beneficiaries",
                keyboardType: KeyboardType.regular,
                inputFormatters: [],
                borderRadius: Sizer.height(20),
              ),
              YBox(6),
              Expanded(
                child: ListView.separated(
                  padding: EdgeInsets.only(
                    top: Sizer.width(16),
                    bottom: Sizer.width(50),
                  ),
                  itemBuilder: (ctx, i) {
                    var beneficiary = beneVm.homeFreqBeneficiaries[i];
                    return SendMoneyBenrficiaryListTile(
                      name: beneficiary.accountName ?? "",
                      title: beneficiary.accountName ?? "",
                      subtitle: " ${beneficiary.accountIdentifier ?? "N/A"}",
                      iconPath: beneficiary.iconUrl ?? "",
                      onTap: () {
                        final fromCurrencyCode =
                            beneficiary.latestTransaction?.currency?.code ?? "";
                        final toCurrencyCode = beneficiary
                                .latestTransaction?.receivedCurrency?.code ??
                            "";
                        Navigator.pushNamed(
                          context,
                          RoutePath.sendMoneyScreen,
                          arguments: SendMoneyArg(
                            fromCurrencyCode: fromCurrencyCode,
                            toCurrencyCode: toCurrencyCode,
                            fromAmount:
                                beneficiary.latestTransaction?.amount ?? "",
                            beneficiary: beneficiary,
                          ),
                        );
                      },
                    );
                  },
                  separatorBuilder: (ctx, _) => const YBox(8),
                  itemCount: beneVm.homeFreqBeneficiaries.length,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
