import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class SendToIbanScreen extends StatefulWidget {
  const SendToIbanScreen({
    super.key,
    required this.arg,
  });

  /// NOTE: if namecheck is true
  /// Add validation for account name
  /// if  false, do no validation
  final TransferMethodArg arg;

  @override
  State<SendToIbanScreen> createState() => _SendToIbanScreenState();
}

class _SendToIbanScreenState extends State<SendToIbanScreen> {
  final firsstNameC = TextEditingController();
  final lastNameC = TextEditingController();
  final ibanC = TextEditingController();

  bool saveBeneficiary = false;

  // verifying iban return true or false;
  bool ibanIsVerified = false;
  String? errorMessage;

  // Timer for debouncing validation
  Timer? _debounceTimer;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _setFirstAndLastNameFromBeneficiary();
    });
  }

  @override
  void dispose() {
    _debounceTimer?.cancel();
    firsstNameC.dispose();
    lastNameC.dispose();
    ibanC.dispose();
    super.dispose();
  }

  verifyIban() async {
    final sendMoneyVm = context.read<SendMoneyVM>();
    final res = await sendMoneyVm.verifyIBAN(
      ibanNUM: ibanC.text,
      firstName: firsstNameC.text,
      lastName: lastNameC.text,
      currencyId: sendMoneyVm.recipientCurrency?.id ?? 0,
      amount: sendMoneyVm.recipientC.text.replaceAllCommas(),
    );

    ibanIsVerified = false;
    errorMessage = null;
    if (!res.success) {
      errorMessage = res.message;
    } else {
      ibanIsVerified = true;
    }

    setState(() {});

    // handleApiResponse(
    //   response: res,
    //   onSuccess: () {
    //     ibanIsVerified = true;
    //     setState(() {});
    //   },
    // );
  }

  // Check if IBAN is filled (required before other fields)
  bool get _isIbanFilled => ibanC.text.trim().isNotEmpty;

  // Check if all required fields are filled
  bool get _areAllFieldsFilled =>
      ibanC.text.trim().isNotEmpty &&
      firsstNameC.text.trim().isNotEmpty &&
      lastNameC.text.trim().isNotEmpty;

  // Listener for field changes with debounce
  void _onFieldChanged() {
    ibanIsVerified = false;
    // Update UI immediately for field enabling/disabling
    setState(() {});

    // Set up debounced validation
    _debounceTimer = Timer(const Duration(milliseconds: 800), () {
      // Auto-call verifyIban when all fields are filled for the first time
      if (_areAllFieldsFilled && !ibanIsVerified) {
        verifyIban();
      }
    });
  }

  String get accountName =>
      "${firsstNameC.text.trim()} ${lastNameC.text.trim()}";

  bool get isFormValid {
    return firsstNameC.text.isNotEmpty &&
        lastNameC.text.isNotEmpty &&
        ibanC.text.isNotEmpty &&
        accountName.isNotEmpty &&
        ibanIsVerified;
  }

  @override
  Widget build(BuildContext context) {
    final beneficiary = widget.arg.beneficiary;
    return Consumer<SendMoneyVM>(builder: (context, vm, _) {
      return BusyOverlay(
        show: vm.isBusy,
        child: Scaffold(
          appBar: NewCustomAppbar(
            showHeaderTitle: true,
            headerText: 'Individual Beneficiary',
            onBackBtnTap: () {
              Navigator.pop(context);
            },
          ),
          body: Column(
            children: [
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      Text(
                        "Please input Account details of your Beneficiary",
                        style: AppTypography.text16
                            .copyWith(color: AppColors.gray93),
                      ),
                      const YBox(40),
                      Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: Sizer.width(24),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            CustomTextField(
                              controller: ibanC,
                              labelText: "IBAN",
                              showLabelHeader: true,
                              isReadOnly: beneficiary != null,
                              fillColor: beneficiary != null
                                  ? AppColors.grayFA
                                  : AppColors.white,
                              hintText: 'Type beneficiary IBAN',
                              borderRadius: Sizer.height(12),
                              suffixIcon: vm.busy(verifyingBankState)
                                  ? const CupertinoActivityIndicator()
                                  : null,
                              onChanged: (val) => setState(() {}),
                              onTap: () {},
                            ),
                            YBox(20),
                            CustomTextField(
                              controller: firsstNameC,
                              labelText: 'First Name',
                              showLabelHeader: true,
                              borderRadius: Sizer.height(12),
                              isReadOnly: !_isIbanFilled,
                              prefixIcon: Icon(
                                Iconsax.user,
                                color: AppColors.gray500,
                                size: Sizer.height(20),
                              ),
                              suffixIcon: vm.busy(verifyingBankState)
                                  ? const CupertinoActivityIndicator()
                                  : null,
                              onChanged: (p0) {
                                AppUtils.handleTextCapitalization(
                                    firsstNameC, p0);
                                _onFieldChanged();
                              },
                              onTap: () {
                                if (!_isIbanFilled) {
                                  showWarningToast('Please enter IBAN first');
                                }
                              },
                            ),
                            const YBox(16),
                            CustomTextField(
                              controller: lastNameC,
                              labelText: 'Last Name',
                              showLabelHeader: true,
                              borderRadius: Sizer.height(12),
                              isReadOnly: !_isIbanFilled,
                              prefixIcon: Icon(
                                Iconsax.user,
                                color: AppColors.gray500,
                                size: Sizer.height(20),
                              ),
                              suffixIcon: vm.busy(verifyingBankState)
                                  ? const CupertinoActivityIndicator()
                                  : null,
                              onChanged: (p0) {
                                AppUtils.handleTextCapitalization(
                                    lastNameC, p0);
                                _onFieldChanged();
                              },
                              onTap: () {
                                if (!_isIbanFilled) {
                                  showWarningToast('Please enter IBAN first');
                                }
                              },
                            ),
                            if (errorMessage != null)
                              Padding(
                                padding: EdgeInsets.only(
                                  top: Sizer.height(8),
                                ),
                                child: Row(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Icon(
                                      Icons.info_outline,
                                      color: AppColors.red,
                                    ),
                                    const XBox(8),
                                    Expanded(
                                      child: Text(
                                        errorMessage!,
                                        style: AppTypography.text14.copyWith(
                                          color: AppColors.red,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              )
                            else if (ibanIsVerified)
                              AccountNameWidget(accountName: accountName),
                          ],
                        ),
                      ),
                      if (beneficiary == null)
                        Padding(
                          padding: EdgeInsets.only(
                            top: Sizer.height(30),
                            left: Sizer.width(24),
                          ),
                          child: Row(
                            children: [
                              CustomSwitch(
                                value: saveBeneficiary,
                                onChanged: (value) {
                                  setState(() {
                                    saveBeneficiary = value;
                                  });
                                },
                              ),
                              const XBox(12),
                              Text(
                                "Save beneficiary",
                                style: AppTypography.text16.copyWith(
                                  color: AppColors.gray500,
                                ),
                              ),
                            ],
                          ),
                        ),
                      const YBox(100),
                    ],
                  ),
                ),
              ),
            ],
          ),
          bottomSheet: Container(
            color: AppColors.white,
            padding: EdgeInsets.symmetric(
              horizontal: Sizer.width(24),
            ).copyWith(
              bottom: Sizer.height(30),
            ),
            child: CustomBtn.withChild(
              onTap: () async {
                FocusScope.of(context).unfocus();

                if (beneficiary == null) {
                  vm.setTransferParams(TransferParams(
                    destinationBankUuid: "iban",
                    accountName: accountName,
                    destinationBankAccountNumber: ibanC.text.trim(),
                    transferMethod: TransferMethod.iban,
                    firstName: firsstNameC.text.trim(),
                    lastName: lastNameC.text.trim(),
                    saveBeneficiary: saveBeneficiary,
                  ));
                  final result = await BsWrapper.bottomSheet(
                    context: context,
                    widget: BeneficiaryScamSheet(),
                  );

                  if (result is bool && result) {
                    Navigator.pushNamed(context, RoutePath.reviewScreen,
                        arguments: SendMoneyReviewsArg(
                          name: accountName,
                          title: accountName,
                          iconPath: "",
                          subTitle: ibanC.text.trim(),
                        ));
                  }

                  return;
                }

                Navigator.pushNamed(
                  context,
                  RoutePath.reviewScreen,
                  arguments: SendMoneyReviewsArg(
                      name: beneficiary.accountName ?? "",
                      title: beneficiary.accountName ?? "",
                      iconPath: beneficiary.iconUrl ?? "",
                      subTitle:
                          "${beneficiary.institutionName} • ${beneficiary.accountIdentifier}"),
                );
              },
              online: isFormValid,
              borderRadius: BorderRadius.circular(Sizer.radius(20)),
              child: ContinueText(isOnline: isFormValid),
            ),
          ),
        ),
      );
    });
  }

  _setFirstAndLastNameFromBeneficiary([Beneficiary? beneficiary]) {
    final sendMoneyVM = context.read<SendMoneyVM>();
    ibanC.text = beneficiary?.institutionCode ?? "";
    firsstNameC.text = beneficiary?.firstName ?? "";
    lastNameC.text = beneficiary?.lastName ?? "";

    sendMoneyVM.autoFillAcctNumberName(widget.arg.beneficiary);

    setState(() {});
  }
}
