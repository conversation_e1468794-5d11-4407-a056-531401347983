import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

enum CurrencyType { all, selected }

class ExchangeRateScreen extends StatefulWidget {
  const ExchangeRateScreen({super.key});

  @override
  State<ExchangeRateScreen> createState() => _ExchangeRateScreenState();
}

class _ExchangeRateScreenState extends State<ExchangeRateScreen>
    with TickerProviderStateMixin {
  late AnimationController _slideController;
  late AnimationController _staggerController;
  List<Animation<Offset>> _itemSlideAnimations = [];
  List<Animation<double>> _itemFadeAnimations = [];
  bool _animationsInitialized = false;
  bool _dataLoaded = false;
  List<CurrencyRates> _filteredCurrencyRates = [];

  final _searchC = TextEditingController();

  @override
  void initState() {
    super.initState();
    _initializeControllers();
    _searchC.addListener(_onSearchChanged);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadDataAndAnimate();
    });
  }

  @override
  void dispose() {
    _slideController.dispose();
    _staggerController.dispose();
    _searchC.removeListener(_onSearchChanged);
    _searchC.dispose();
    super.dispose();
  }

  void _initializeControllers() {
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _staggerController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );
  }

  void _initializeAnimations(int itemCount) {
    if (_animationsInitialized) return;

    _itemSlideAnimations = List.generate(
      itemCount,
      (index) =>
          Tween<Offset>(begin: const Offset(-0.5, 0), end: Offset.zero).animate(
        CurvedAnimation(
          parent: _staggerController,
          curve: Interval(
            index * 0.15,
            (index * 0.15) + 0.5,
            curve: Curves.easeOutCubic,
          ),
        ),
      ),
    );

    _itemFadeAnimations = List.generate(
      itemCount,
      (index) => Tween<double>(begin: 0.0, end: 1.0).animate(
        CurvedAnimation(
          parent: _staggerController,
          curve: Interval(
            index * 0.15,
            (index * 0.15) + 0.6,
            curve: Curves.easeOut,
          ),
        ),
      ),
    );

    _animationsInitialized = true;
  }

  void _onSearchChanged() {
    final currencyVM = context.read<CurrencyVM>();
    final query = _searchC.text.toLowerCase().trim();

    setState(() {
      if (query.isEmpty) {
        _filteredCurrencyRates =
            List.from(currencyVM.isCreatableCurrenciesRates);
      } else {
        _filteredCurrencyRates =
            currencyVM.isCreatableCurrenciesRates.where((currencyRate) {
          // Search by currency code (e.g., "CAD", "USD")
          final codeMatch =
              currencyRate.code?.toLowerCase().contains(query) ?? false;

          // Search by currency name (e.g., "Canadian Dollar")
          // final nameMatch =
          //     currencyRate.name?.toLowerCase().contains(query) ?? false;

          // Search by country (e.g., "Canada")
          final countryMatch =
              currencyRate.country?.toLowerCase().contains(query) ?? false;

          // Search by currency symbol (e.g., "$", "₦")
          // final symbolMatch =
          //     currencyRate.symbol?.toLowerCase().contains(query) ?? false;

          return codeMatch || countryMatch;
        }).toList();
      }

      // Always re-initialize animations for any change
      _animationsInitialized = false;
      if (_filteredCurrencyRates.isNotEmpty) {
        _initializeAnimations(_filteredCurrencyRates.length);
        _staggerController.reset();
        _staggerController.forward();
      }
    });
  }

  Future<void> _loadDataAndAnimate() async {
    final currencyVM = context.read<CurrencyVM>();
    final response = await currencyVM.getCurrenciesRates();

    if (response.success && mounted) {
      setState(() {
        _dataLoaded = true;
        _filteredCurrencyRates =
            List.from(currencyVM.isCreatableCurrenciesRates);
      });

      // Initialize animations with actual data length
      _initializeAnimations(_filteredCurrencyRates.length);

      // Start the stagger animation
      await Future.delayed(const Duration(milliseconds: 200));
      if (mounted) {
        _staggerController.forward();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<CurrencyVM>(builder: (context, vm, _) {
      printty('currenciesRates ${vm.isCreatableCurrenciesRates.length}');

      // Show loading state until data is loaded
      if (!_dataLoaded || vm.isCreatableCurrenciesRates.isEmpty) {
        return BusyOverlay(
          show: true,
          child: Scaffold(
            appBar: NewCustomAppbar(
              showHeaderTitle: true,
              headerText: 'Exchange Rates',
            ),
            body: const Center(
              child: CircularProgressIndicator(),
            ),
          ),
        );
      }

      return BusyOverlay(
        show: false,
        child: Scaffold(
          appBar: NewCustomAppbar(
            showHeaderTitle: true,
            headerText: 'Exchange Rates',
          ),
          body: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const YBox(10),
              Padding(
                padding: EdgeInsets.symmetric(
                  horizontal: Sizer.width(24),
                ),
                child: CustomTextField(
                  controller: _searchC,
                  prefixIcon: Padding(
                    padding: EdgeInsets.all(Sizer.radius(12)),
                    child: SvgPicture.asset(AppSvgs.search),
                  ),
                  hintText: "Search by currency, code, country, or rate...",
                  keyboardType: KeyboardType.regular,
                  inputFormatters: [],
                  borderRadius: Sizer.height(12),
                ),
              ),
              const YBox(10),
              // SingleChildScrollView(
              //   scrollDirection: Axis.horizontal,
              //   child: Container(
              //     padding: EdgeInsets.only(
              //       left: Sizer.width(24),
              //     ),
              //     child: Row(
              //       children: [
              //         TextWithFlagBtn(
              //           text: "All",
              //           horizontalPadding: 24,
              //           isSelected: _selectedIndex == -1,
              //           onNavigate: () {
              //             setState(() {
              //               _selectedIndex = -1;
              //               _currencyType = CurrencyType.all;
              //             });
              //           },
              //         ),
              //         ...List.generate(vm.isCreatableCurrenciesRates.length,
              //             (index) {
              //           var currencyRate =
              //               vm.isCreatableCurrenciesRates[index];
              //           return TextWithFlagBtn(
              //             text: currencyRate.code ?? '',
              //             imgPath: currencyRate.flag ?? '',
              //             isSelected: _selectedIndex == index,
              //             onNavigate: () {
              //               setState(() {
              //                 _selectedIndex = index;
              //                 _currencyType = CurrencyType.selected;
              //               });
              //               vm.setSelectedCurrencyRate(
              //                   currencyRate.code ?? '');
              //             },
              //           );
              //         }),
              //       ],
              //     ),
              //   ),
              // ),
              // const YBox(30),
              // if (_currencyType == CurrencyType.all)
              Expanded(
                child: _filteredCurrencyRates.isEmpty &&
                        _searchC.text.isNotEmpty
                    ? _buildEmptySearchState()
                    : ListView.separated(
                        shrinkWrap: true,
                        padding: EdgeInsets.only(
                          top: Sizer.height(10),
                          bottom: Sizer.height(50),
                          left: Sizer.width(24),
                          right: Sizer.width(24),
                        ),
                        itemBuilder: (ctx, i) {
                          var currencyRate = _filteredCurrencyRates[i];

                          // Ensure animations are available for this index
                          if (i >= _itemSlideAnimations.length ||
                              i >= _itemFadeAnimations.length) {
                            return Container(); // Return empty container if animations not ready
                          }

                          return SlideTransition(
                            position: _itemSlideAnimations[i],
                            child: FadeTransition(
                              opacity: _itemFadeAnimations[i],
                              child: Column(
                                children: List.generate(
                                  currencyRate.rates.length,
                                  (i) {
                                    return Container(
                                      padding: EdgeInsets.only(
                                        bottom: Sizer.height(30),
                                      ),
                                      child: Row(
                                        children: [
                                          Row(
                                            children: [
                                              // _currencyText(
                                              //   imgPath: currencyRate.flag,
                                              //   text: currencyRate.code,
                                              // ),
                                              // const XBox(2),
                                              StackedCurrencyFlag(
                                                width: 24,
                                                height: 24,
                                                firstFlag: currencyRate.flag,
                                                secondFlag:
                                                    currencyRate.rates[i].flag,
                                              ),
                                              XBox(30),
                                              Text(
                                                "${currencyRate.code}",
                                                style: AppTypography.text12
                                                    .copyWith(
                                                  color: AppColors.gray51,
                                                  fontWeight: FontWeight.w500,
                                                ),
                                              ),
                                              XBox(8),
                                              const Icon(Iconsax.arrow_right_1),
                                              XBox(8),
                                              Text(
                                                "${currencyRate.rates[i].to}",
                                                style: AppTypography.text12
                                                    .copyWith(
                                                  color: AppColors.gray51,
                                                  fontWeight: FontWeight.w500,
                                                ),
                                              ),
                                              // const XBox(4),
                                              // _currencyText(
                                              //   imgPath: currencyRate.rates[i].flag,
                                              //   text: currencyRate.rates[i].to,
                                              // ),
                                            ],
                                          ),
                                          Spacer(),
                                          Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            children: [
                                              Text(
                                                AppUtils
                                                    .formatAmountDoubleString(
                                                        currencyRate.rates[i]
                                                                .rate ??
                                                            "0"),
                                                style: AppTypography.text14
                                                    .copyWith(
                                                  color: AppColors.mainBlack,
                                                  fontWeight: FontWeight.w500,
                                                ),
                                              ),
                                              XBox(16),
                                              TextWithFlagBtn(
                                                toCurrencyCode:
                                                    currencyRate.rates[i].to ??
                                                        '',
                                                onNavigate: () {
                                                  Navigator.pushNamed(
                                                    context,
                                                    RoutePath.sendMoneyScreen,
                                                    arguments: SendMoneyArg(
                                                      fromCurrencyCode:
                                                          currencyRate.code ??
                                                              '',
                                                      toCurrencyCode:
                                                          currencyRate.rates[i]
                                                                  .to ??
                                                              '',
                                                    ),
                                                  );
                                                },
                                              ),
                                            ],
                                          ),
                                        ],
                                      ),
                                    );
                                  },
                                ),
                              ),
                            ),
                          );
                        },
                        separatorBuilder: (ctx, _) => const SizedBox.shrink(),
                        itemCount: _filteredCurrencyRates.length,
                      ),
              ),
              // if (_currencyType == CurrencyType.selected &&
              //     vm.selectedCurrencyRate != null)
              //   Expanded(
              //     child: ListView.separated(
              //       shrinkWrap: true,
              //       padding: EdgeInsets.symmetric(
              //         horizontal: Sizer.width(24),
              //       ),
              //       itemBuilder: (ctx, i) {
              //         var selectedRate = vm.selectedCurrencyRate;
              //         return Row(
              //           children: [
              //             Expanded(
              //               child: Row(
              //                 children: [
              //                   _currencyText(
              //                     imgPath: selectedRate?.flag,
              //                     text: selectedRate?.code,
              //                   ),
              //                   const XBox(2),
              //                   const Icon(Iconsax.arrow_right_1),
              //                   const XBox(4),
              //                   _currencyText(
              //                     imgPath: selectedRate?.rates[i].flag,
              //                     text: selectedRate?.rates[i].to,
              //                   ),
              //                 ],
              //               ),
              //             ),
              //             const XBox(20),
              //             Expanded(
              //               child: actionText(
              //                 amount: AppUtils.formatAmountDoubleString(
              //                     selectedRate?.rates[i].rate ?? '0'),
              //                 onNavigate: () {
              //                   Navigator.pushNamed(
              //                     context,
              //                     RoutePath.sendMoneyScreen,
              //                     arguments: SendMoneyArg(
              //                       fromCurrencyCode:
              //                           selectedRate?.code ?? '',
              //                       toCurrencyCode:
              //                           selectedRate?.rates[i].to ?? '',
              //                     ),
              //                   );
              //                 },
              //               ),
              //             ),
              //           ],
              //         );
              //       },
              //       separatorBuilder: (ctx, _) => const YBox(30),
              //       itemCount: vm.selectedCurrencyRate?.rates.length ?? 0,
              //     ),
              //   ),
            ],
          ),
        ),
      );
    });
  }

  Widget _buildEmptySearchState() {
    return Center(
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: Sizer.width(40)),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SvgPicture.asset(
              AppSvgs.search,
              height: Sizer.height(60),
              width: Sizer.width(60),
              colorFilter: ColorFilter.mode(
                AppColors.gray51.withValues(alpha: 0.5),
                BlendMode.srcIn,
              ),
            ),
            const YBox(20),
            Text(
              "No results found",
              style: AppTypography.text18.copyWith(
                color: AppColors.gray51,
                fontWeight: FontWeight.w600,
              ),
            ),
            const YBox(8),
            Text(
              "Try searching with different keywords like currency codes (CAD, USD), country names, or exchange rates.",
              textAlign: TextAlign.center,
              style: AppTypography.text14.copyWith(
                color: AppColors.gray51.withValues(alpha: 0.8),
                fontWeight: FontWeight.w400,
              ),
            ),
            const YBox(20),
            InkWell(
              onTap: () {
                _searchC.clear();
              },
              child: Container(
                padding: EdgeInsets.symmetric(
                  horizontal: Sizer.width(20),
                  vertical: Sizer.height(10),
                ),
                decoration: BoxDecoration(
                  border: Border.all(color: AppColors.primaryBlue),
                  borderRadius: BorderRadius.circular(Sizer.radius(8)),
                ),
                child: Text(
                  "Clear Search",
                  style: AppTypography.text14.copyWith(
                    color: AppColors.primaryBlue,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class TextWithFlagBtn extends StatelessWidget {
  const TextWithFlagBtn({
    super.key,
    this.onNavigate,
    required this.toCurrencyCode,
  });

  final String toCurrencyCode;
  final VoidCallback? onNavigate;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onNavigate,
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: Sizer.width(10),
          vertical: Sizer.height(4),
        ),
        decoration: BoxDecoration(
          border: Border.all(
            color: AppColors.primaryBlue,
          ),
          borderRadius: BorderRadius.circular(Sizer.radius(14)),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              "Send $toCurrencyCode",
              style: AppTypography.text12.copyWith(
                color: AppColors.primaryBlue,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
