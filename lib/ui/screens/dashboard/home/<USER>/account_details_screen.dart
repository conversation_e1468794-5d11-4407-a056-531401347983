import 'package:flutter/services.dart';
import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';
import 'package:share_plus/share_plus.dart';

class AccountDetailsScreen extends StatelessWidget {
  const AccountDetailsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<WalletVM>(builder: (context, vm, _) {
      return Scaffold(
        appBar: NewCustomAppbar(
          showHeaderTitle: true,
          headerText: 'Your Account Details',
        ),
        body: Padding(
          padding: EdgeInsets.symmetric(
            horizontal: Sizer.width(24),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                "Make a Bank transfer to the account displayed below",
                style: AppTypography.text15.copyWith(color: AppColors.gray93),
              ),
              const YBox(40),
              Expanded(
                child: LoadableContentBuilder(
                  isBusy: vm.isBusy,
                  isError: false,
                  items: vm.nairaWallet?.virtualAccounts ?? [],
                  loadingBuilder: (ctx) {
                    return SizedBox(
                      height: Sizer.height(200),
                      child: const Center(
                        child: CircularProgressIndicator(),
                      ),
                    );
                  },
                  emptyBuilder: (ctx) {
                    return Center(
                      child: EmptyState(
                        title: "No virtual account found",
                      ),
                    );
                  },
                  contentBuilder: (context) {
                    return Column(
                      children: [
                        Container(
                          decoration: BoxDecoration(
                            border: Border.all(
                              color: AppColors.blueFD,
                            ),
                            borderRadius: BorderRadius.only(
                              topLeft: Radius.circular(4),
                              topRight: Radius.circular(4),
                              bottomLeft: Radius.circular(16),
                              bottomRight: Radius.circular(16),
                            ),
                          ),
                          child: Column(
                            children: [
                              Container(
                                padding: EdgeInsets.symmetric(
                                  horizontal: Sizer.width(20),
                                  vertical: Sizer.height(12),
                                ),
                                decoration: BoxDecoration(
                                  color: AppColors.primaryBlue90,
                                  borderRadius: BorderRadius.only(
                                    topLeft: Radius.circular(4),
                                    topRight: Radius.circular(4),
                                    bottomLeft: Radius.circular(16),
                                    bottomRight: Radius.circular(16),
                                  ),
                                ),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Row(
                                      children: [
                                        SvgPicture.asset(
                                          AppSvgs.ngn,
                                          height: Sizer.height(16),
                                          width: Sizer.width(22),
                                        ),
                                        const XBox(10),
                                        Text(
                                          "Nigerian Naira",
                                          style: AppTypography.text10.medium
                                              .copyWith(
                                            color: AppColors.white,
                                          ),
                                        ),
                                      ],
                                    ),
                                    YBox(12),
                                    Text(
                                      "Account Number",
                                      style: AppTypography.text12.medium
                                          .copyWith(
                                              color: AppColors.white
                                                  .withValues(alpha: 0.8)),
                                    ),
                                    YBox(4),
                                    Row(
                                      children: [
                                        Text(
                                          vm.nairaWallet?.virtualAccounts?[0]
                                                  .accountNumber ??
                                              "",
                                          style: AppTypography.text20.semiBold
                                              .copyWith(
                                            color: AppColors.white,
                                          ),
                                        ),
                                        Spacer(),
                                        InkWell(
                                          onTap: () {
                                            Clipboard.setData(ClipboardData(
                                              text: vm
                                                      .nairaWallet
                                                      ?.virtualAccounts?[0]
                                                      .accountNumber ??
                                                  "",
                                            ));
                                            showSuccessToastMessage(
                                                "Account number copied");
                                          },
                                          child: SvgPicture.asset(AppSvgs.copy),
                                        )
                                      ],
                                    )
                                  ],
                                ),
                              ),
                              YBox(4),
                              Padding(
                                padding: EdgeInsets.symmetric(
                                  horizontal: Sizer.width(20),
                                  vertical: Sizer.height(12),
                                ),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Align(
                                      alignment: Alignment.centerLeft,
                                      child: _buildAccountdetails(
                                        title: "Bank Name",
                                        subTitle: vm
                                                .nairaWallet
                                                ?.virtualAccounts?[0]
                                                .bankName ??
                                            "",
                                      ),
                                    ),
                                    YBox(16),
                                    Align(
                                      alignment: Alignment.centerLeft,
                                      child: _buildAccountdetails(
                                        title: "Account Name",
                                        subTitle: vm
                                                .nairaWallet
                                                ?.virtualAccounts?[0]
                                                .accountName ??
                                            "",
                                      ),
                                    ),
                                    YBox(8),
                                  ],
                                ),
                              )
                            ],
                          ),
                        ),
                        YBox(20),
                        Container(
                          padding: EdgeInsets.all(Sizer.radius(20)),
                          decoration: BoxDecoration(
                            color: AppColors.grayFCF,
                            border: Border.all(
                              color: AppColors.blueFD,
                            ),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Column(
                            children: [
                              Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  SvgPicture.asset(
                                    AppSvgs.infoBlue,
                                    height: Sizer.height(16),
                                    width: Sizer.width(16),
                                  ),
                                  XBox(6),
                                  Expanded(
                                    child: Text(
                                      "Only banks in Nigeria can be used to send money to this account",
                                      style: AppTypography.text12.copyWith(
                                        color: AppColors.gray93,
                                      ),
                                    ),
                                  )
                                ],
                              ),
                              YBox(16),
                              Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  SvgPicture.asset(
                                    AppSvgs.infoBlue,
                                    height: Sizer.height(16),
                                    width: Sizer.width(16),
                                  ),
                                  XBox(6),
                                  Expanded(
                                    child: Text(
                                      "Funds are usually delivered in just a few seconds",
                                      style: AppTypography.text12.copyWith(
                                        color: AppColors.gray93,
                                      ),
                                    ),
                                  )
                                ],
                              )
                            ],
                          ),
                        ),
                        YBox(28),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              "This account is issued and serviced by",
                              style: AppTypography.text12.copyWith(
                                color: AppColors.gray93,
                              ),
                            ),
                            XBox(8),
                            SvgPicture.asset(
                              AppSvgs.paga,
                              height: Sizer.height(14),
                            )
                          ],
                        ),
                        YBox(120),
                        CustomBtn.withChild(
                          borderRadius: BorderRadius.circular(20),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              SvgPicture.asset(
                                AppSvgs.share,
                                colorFilter: ColorFilter.mode(
                                    AppColors.white, BlendMode.srcIn),
                              ),
                              XBox(8),
                              Text(
                                "Share Details",
                                style: AppTypography.text16.medium.copyWith(
                                  color: AppColors.white,
                                ),
                              ),
                            ],
                          ),
                          onTap: () async {
                            Share.share(
                              "Account Number: ${vm.nairaWallet?.virtualAccounts?[0].accountNumber} \nAccount Name: ${vm.nairaWallet?.virtualAccounts?[0].accountName} \nBank Name: ${vm.nairaWallet?.virtualAccounts?[0].bankName}",
                              subject: "Korrency",
                            );
                          },
                        ),
                      ],
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      );
    });
  }

  Widget _buildAccountdetails({
    required String title,
    required String subTitle,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: AppTypography.text12.copyWith(
            color: AppColors.gray93,
          ),
        ),
        YBox(4),
        Text(
          subTitle.toUpperCase(),
          style: AppTypography.text12.bold.copyWith(
            color: AppColors.gray51,
          ),
        ),
      ],
    );
  }
}
