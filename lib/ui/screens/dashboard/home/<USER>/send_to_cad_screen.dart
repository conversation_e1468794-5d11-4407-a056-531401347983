import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class SendToCadScreen extends StatefulWidget {
  const SendToCadScreen({
    super.key,
    required this.transferMethodArg,
  });

  final TransferMethodArg transferMethodArg;

  @override
  State<SendToCadScreen> createState() => _SendToCadScreenState();
}

class _SendToCadScreenState extends State<SendToCadScreen> {
  final interacEmailC = TextEditingController();
  final interacFirstNameC = TextEditingController();
  final interacLastNameC = TextEditingController();

  bool saveBeneficiary = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      printty("beneficiary: ${widget.transferMethodArg.beneficiary}");
      if (widget.transferMethodArg.beneficiary != null) {
        _syncInteracBeneficiary();
      }
    });
  }

  @override
  void dispose() {
    interacEmailC.dispose();
    interacFirstNameC.dispose();
    interacLastNameC.dispose();

    super.dispose();
  }

  bool get isInteracEmaiLValid =>
      interacEmailC.text.contains("@") &&
      interacEmailC.text.contains(".") &&
      interacEmailC.text.split('.').last.isNotEmpty;

  bool get cadButtonIsActive =>
      isInteracEmaiLValid &&
      interacEmailC.text.trim().isNotEmpty &&
      interacFirstNameC.text.trim().isNotEmpty &&
      interacLastNameC.text.trim().isNotEmpty;

  @override
  Widget build(BuildContext context) {
    final beneficiary = widget.transferMethodArg.beneficiary;
    return Consumer<SendMoneyVM>(builder: (context, vm, _) {
      return BusyOverlay(
        show: vm.isBusy,
        child: Scaffold(
          appBar: NewCustomAppbar(
            showHeaderTitle: true,
            headerText: "Interac e-Transfer",
          ),
          body: SafeArea(
            bottom: false,
            child: ListView(
              padding: EdgeInsets.only(
                left: Sizer.width(24),
                right: Sizer.width(24),
              ),
              children: [
                Text(
                  "Please input interac details of your Beneficiary",
                  style: AppTypography.text16.copyWith(color: AppColors.gray93),
                ),
                const YBox(40),
                CustomTextField(
                  labelText: "Beneficiary Email",
                  showLabelHeader: true,
                  borderRadius: Sizer.height(12),
                  controller: interacEmailC,
                  errorText: _errortext(),
                  isReadOnly: beneficiary != null,
                  fillColor: beneficiary != null ? AppColors.grayFA : null,
                  prefixIcon: Icon(
                    Iconsax.sms,
                    color: AppColors.gray500,
                    size: Sizer.height(20),
                  ),
                  onChanged: (v) => vm.reBuildUI(),
                ),
                const YBox(16),
                CustomTextField(
                    labelText: "First Name",
                    showLabelHeader: true,
                    borderRadius: Sizer.height(12),
                    controller: interacFirstNameC,
                    isReadOnly: beneficiary != null,
                    fillColor: beneficiary != null ? AppColors.grayFA : null,
                    prefixIcon: Icon(
                      Iconsax.user,
                      color: AppColors.gray500,
                      size: Sizer.height(20),
                    ),
                    onChanged: (v) {
                      AppUtils.handleTextCapitalization(interacFirstNameC, v);
                      setState(() {});
                    }),
                const YBox(16),
                CustomTextField(
                  labelText: "Last Name",
                  showLabelHeader: true,
                  borderRadius: Sizer.height(12),
                  controller: interacLastNameC,
                  isReadOnly: beneficiary != null,
                  fillColor: beneficiary != null ? AppColors.grayFA : null,
                  prefixIcon: Icon(
                    Iconsax.user,
                    color: AppColors.gray500,
                    size: Sizer.height(20),
                  ),
                  onChanged: (v) {
                    AppUtils.handleTextCapitalization(interacLastNameC, v);
                    setState(() {});
                  },
                ),
                if (beneficiary == null)
                  Padding(
                    padding: EdgeInsets.only(
                      top: Sizer.height(70),
                    ),
                    child: Row(
                      children: [
                        CustomSwitch(
                          value: saveBeneficiary,
                          onChanged: (value) {
                            saveBeneficiary = value;
                            setState(() {});
                          },
                        ),
                        const XBox(12),
                        Text(
                          "Save beneficiary",
                          style: AppTypography.text16.copyWith(
                            color: AppColors.gray500,
                          ),
                        ),
                      ],
                    ),
                  ),
              ],
            ),
          ),
          bottomSheet: Container(
            padding: EdgeInsets.only(
              left: Sizer.width(24),
              right: Sizer.width(24),
              bottom: Sizer.height(30),
            ),
            child: CustomBtn.withChild(
              onTap: () async {
                FocusScope.of(context).unfocus();
                if (beneficiary == null) {
                  final result = await BsWrapper.bottomSheet(
                    context: context,
                    widget: BeneficiaryScamSheet(),
                  );

                  if (result is bool && result) {
                    vm.autoFillInteracArgs(
                      Beneficiary(
                        accountIdentifier: interacEmailC.text,
                        firstName: interacFirstNameC.text,
                        lastName: interacLastNameC.text,
                      ),
                      saveBeneficiary: saveBeneficiary,
                    );

                    Navigator.pushNamed(
                      context,
                      RoutePath.reviewScreen,
                      arguments: SendMoneyReviewsArg(
                        name:
                            "${interacFirstNameC.text} ${interacLastNameC.text}",
                        title:
                            "${interacFirstNameC.text} ${interacLastNameC.text}",
                        subTitle: interacEmailC.text,
                        iconPath: AppUtils.kInteracPng,
                      ),
                    );
                  }

                  return;
                }

                Navigator.pushNamed(
                  context,
                  RoutePath.reviewScreen,
                  arguments: SendMoneyReviewsArg(
                    name: beneficiary.accountName ?? "",
                    title: beneficiary.accountName ?? "",
                    iconPath: beneficiary.iconUrl ?? "",
                    subTitle: beneficiary.accountIdentifier ?? "N/A",
                  ),
                );
              },
              online: cadButtonIsActive && !_checkDepositInterac(),
              borderRadius: BorderRadius.circular(Sizer.radius(20)),
              child: ContinueText(
                  isOnline: cadButtonIsActive && !_checkDepositInterac()),
            ),
          ),
        ),
      );
    });
  }

  _syncInteracBeneficiary() {
    final sendmoneyVm = context.read<SendMoneyVM>();
    final beneficiary = widget.transferMethodArg.beneficiary;

    interacEmailC.text = beneficiary?.accountIdentifier ?? "";
    interacFirstNameC.text = beneficiary?.firstName ?? "";
    interacLastNameC.text = beneficiary?.lastName ?? "";

    sendmoneyVm.autoFillInteracArgs(widget.transferMethodArg.beneficiary);
  }

  String? _errortext() {
    if (interacEmailC.text.isNotEmpty && !isInteracEmaiLValid) {
      return 'Invalid email';
    } else if (interacEmailC.text.isNotEmpty &&
        (!isInteracEmaiLValid || _checkDepositInterac())) {
      return 'You cannot use the inputted interac email as a recipient';
    } else {
      return null;
    }
  }

  bool _checkDepositInterac() {
    return interacEmailC.text.trim() ==
        context.read<ConfigVM>().interacDepositEmail;
  }
}
