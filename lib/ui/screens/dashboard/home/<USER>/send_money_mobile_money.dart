import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class SendMoneyMobileMoneyScreen extends StatefulWidget {
  const SendMoneyMobileMoneyScreen({
    super.key,
    required this.arg,
  });

  final TransferMethodArg arg;

  @override
  State<SendMoneyMobileMoneyScreen> createState() =>
      _SendMoneyMobileMoneyScreenState();
}

class _SendMoneyMobileMoneyScreenState
    extends State<SendMoneyMobileMoneyScreen> {
  final operatorC = TextEditingController();
  final mobileNumberC = TextEditingController();
  final recipientFirstNameC = TextEditingController();
  final recipientLastNameC = TextEditingController();
  final operatorF = FocusNode();
  final mobileNumberF = FocusNode();
  final recipientFirstNameF = FocusNode();
  final recipientLastNameF = FocusNode();

  bool hasStartedVerification = false;
  bool isMobileNumberValid = false;
  bool mobileNumberHasBeenTapped = false;
  MobileMoney? selectedMobileMoney;

  int selectedBeneficiaryIndex = -1; // -1 means no beneficiary seleccted
  bool saveBeneficiary = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final sendMoneyVM = context.read<SendMoneyVM>();
      final bankVm = context.read<BankVM>();
      bankVm.getMNO(sendMoneyVM.recipientCurrency?.id ?? 0);
      _setFirstAndLastNameFromBeneficiary(widget.arg.beneficiary);
    });
  }

  @override
  void dispose() {
    operatorC.dispose();
    mobileNumberC.dispose();
    recipientFirstNameC.dispose();
    recipientLastNameC.dispose();

    operatorF.dispose();
    mobileNumberF.dispose();
    recipientFirstNameF.dispose();
    recipientLastNameF.dispose();

    super.dispose();
  }

  bool get enableButtton =>
      recipientFirstNameC.text.trim().isNotEmpty &&
      recipientLastNameC.text.trim().isNotEmpty &&
      operatorC.text.trim().isNotEmpty &&
      mobileNumberC.text.trim().isNotEmpty;

  @override
  Widget build(BuildContext context) {
    final beneficiary = widget.arg.beneficiary;
    return Consumer<SendMoneyVM>(builder: (context, vm, _) {
      return BusyOverlay(
        show: vm.isBusy,
        child: Scaffold(
          appBar: NewCustomAppbar(
            showHeaderTitle: true,
            headerText: 'Send to ${vm.recipientCurrency?.code} Mobile Money',
            onBackBtnTap: () {
              vm.setMobileMoney(null);
              Navigator.pop(context);
            },
          ),
          body: Column(
            children: [
              Expanded(
                child: ListView(
                  padding: EdgeInsets.symmetric(
                    horizontal: Sizer.width(24),
                  ),
                  children: [
                    Text(
                      "Please input the details of your Beneficiary",
                      style: AppTypography.text16
                          .copyWith(color: AppColors.gray93),
                    ),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const YBox(24),
                        CustomTextField(
                          controller: operatorC,
                          focusNode: operatorF,
                          labelText: "Select Operator",
                          showLabelHeader: true,
                          isReadOnly: true,
                          hintText: 'Select Operator',
                          borderRadius: Sizer.height(12),
                          showSuffixIcon: true,
                          fillColor: AppColors.grayFA,
                          prefixIcon: Padding(
                            padding: EdgeInsets.symmetric(
                              // vertical: Sizer.height(12),
                              horizontal: Sizer.width(10),
                            ),
                            child: SvgPicture.asset(AppSvgs.clipboard),
                          ),
                          onTap: () async {
                            final res = await BsWrapper.bottomSheet(
                              context: context,
                              widget: MobileMoneySheet(
                                  title: operatorC.text.trim()),
                            );
                            if (res is MobileMoney) {
                              operatorC.text = res.name ?? '';
                              selectedMobileMoney = res;
                              if (mobileNumberC.text.length > 9 &&
                                  widget.arg.paymentMethod.nameCheck == true) {
                                verifyMobileWalletByCurrecyId();
                              }

                              selectedBeneficiaryIndex = -1;
                              setState(() {});
                            }
                          },
                        ),
                        const YBox(16),
                        CustomTextField(
                          focusNode: mobileNumberF,
                          controller: mobileNumberC,
                          labelText: "Mobile Number",
                          showLabelHeader: true,
                          isReadOnly: operatorC.text.trim().isEmpty,
                          keyboardType: KeyboardType.number,
                          inputFormatters: [
                            FilteringTextInputFormatter.digitsOnly,
                            LengthLimitingTextInputFormatter(11),
                          ],
                          hintText: '**********',
                          borderRadius: Sizer.height(12),
                          errorText: mobileNumberHasBeenTapped &&
                                  operatorC.text.trim().isEmpty
                              ? 'Please select operator'
                              : null,
                          suffixIcon: vm.busy(verifyingBankState)
                              ? const CupertinoActivityIndicator()
                              : null,
                          prefixIcon: Icon(
                            Iconsax.mobile,
                            color: AppColors.gray500,
                            size: Sizer.height(20),
                          ),
                          onChanged: (val) {
                            hasStartedVerification = false;

                            selectedBeneficiaryIndex = -1;
                            if (val.trim().length > 9 &&
                                widget.arg.paymentMethod.nameCheck == true) {
                              verifyMobileWalletByCurrecyId();
                            }
                            setState(() {});
                          },
                          onTap: () {
                            if (operatorC.text.trim().isEmpty) {
                              mobileNumberHasBeenTapped = true;
                              setState(() {});
                            }
                          },
                        ),
                        const YBox(4),
                        if (hasStartedVerification &&
                            widget.arg.paymentMethod.nameCheck == true)
                          Row(
                            children: [
                              Text(
                                isMobileNumberValid ? 'Active' : 'Not active',
                                style: AppTypography.text12.copyWith(
                                  color: isMobileNumberValid
                                      ? AppColors.baseGreen
                                      : AppColors.red,
                                ),
                              ),
                              const XBox(4),
                              Icon(
                                isMobileNumberValid
                                    ? Icons.check_circle
                                    : Icons.error,
                                size: Sizer.radius(13),
                                color: isMobileNumberValid
                                    ? AppColors.baseGreen
                                    : AppColors.red,
                              ),
                            ],
                          ),
                        const YBox(16),
                        CustomTextField(
                            focusNode: recipientFirstNameF,
                            controller: recipientFirstNameC,
                            labelText: 'First Name',
                            showLabelHeader: true,
                            hintText: 'First Name',
                            borderRadius: Sizer.height(12),
                            prefixIcon: Icon(
                              Iconsax.user,
                              color: AppColors.gray500,
                              size: Sizer.height(20),
                            ),
                            onChanged: (p0) {
                              AppUtils.handleTextCapitalization(
                                  recipientFirstNameC, p0);

                              selectedBeneficiaryIndex = -1;
                              setState(() {});
                            }),
                        const YBox(16),
                        CustomTextField(
                            focusNode: recipientLastNameF,
                            controller: recipientLastNameC,
                            labelText: 'Last Name',
                            showLabelHeader: true,
                            hintText: 'Last Name',
                            borderRadius: Sizer.height(12),
                            prefixIcon: Icon(
                              Iconsax.user,
                              color: AppColors.gray500,
                              size: Sizer.height(20),
                            ),
                            onChanged: (p0) {
                              AppUtils.handleTextCapitalization(
                                  recipientLastNameC, p0);

                              selectedBeneficiaryIndex = -1;
                              setState(() {});
                            }),
                      ],
                    ),
                    const YBox(70),
                    if (beneficiary == null)
                      Row(
                        children: [
                          CustomSwitch(
                            value: saveBeneficiary,
                            onChanged: (value) {
                              saveBeneficiary = value;
                              setState(() {});
                            },
                          ),
                          const XBox(12),
                          Text(
                            "Save beneficiary",
                            style: AppTypography.text16.copyWith(
                              color: AppColors.gray500,
                            ),
                          ),
                        ],
                      ),
                  ],
                ),
              ),
            ],
          ),
          bottomSheet: Container(
            color: AppColors.white,
            padding: EdgeInsets.symmetric(
              horizontal: Sizer.width(24),
            ).copyWith(
              bottom: Sizer.height(30),
            ),
            child: CustomBtn.withChild(
              onTap: () async {
                if (beneficiary == null) {
                  final result = await BsWrapper.bottomSheet(
                    context: context,
                    widget: BeneficiaryScamSheet(),
                  );

                  if (result is bool && result) {
                    vm.setMobileMoney(MobileMoney(
                      saveBeneficiary: saveBeneficiary,
                      uuid: selectedMobileMoney?.uuid ?? '',
                      name: selectedMobileMoney?.name ?? '',
                      mobileNumber: mobileNumberC.text.trim(),
                      recipientName:
                          '${recipientFirstNameC.text.trim()} ${recipientLastNameC.text.trim()}',
                    ));

                    Navigator.pushNamed(context, RoutePath.reviewScreen,
                        arguments: SendMoneyReviewsArg(
                          name:
                              '${recipientFirstNameC.text.trim()} ${recipientLastNameC.text.trim()}',
                          title:
                              '${recipientFirstNameC.text.trim()} ${recipientLastNameC.text.trim()}',
                          iconPath: "",
                          subTitle:
                              "${selectedMobileMoney?.name} • ${mobileNumberC.text.trim()}",
                        ));
                  }

                  return;
                }
                Navigator.pushNamed(context, RoutePath.reviewScreen,
                    arguments: SendMoneyReviewsArg(
                      name: beneficiary.accountName ?? '',
                      title: beneficiary.accountName ?? '',
                      iconPath: beneficiary.iconUrl ?? "",
                      subTitle:
                          "${beneficiary.institutionName} • ${beneficiary.accountIdentifier ?? ''}",
                    ));
              },
              online: enableButtton,
              borderRadius: BorderRadius.circular(Sizer.radius(20)),
              child: ContinueText(isOnline: enableButtton),
            ),
          ),
        ),
      );
    });
  }

  _setFirstAndLastNameFromBeneficiary([Beneficiary? beneficiary]) {
    final vm = context.read<SendMoneyVM>();
    operatorC.text = beneficiary?.institutionName ?? "";
    mobileNumberC.text = beneficiary?.accountIdentifier ?? "";
    recipientFirstNameC.text = (beneficiary?.firstName ?? "").isNotEmpty
        ? (beneficiary?.firstName ?? "").capitalize()
        : "";
    recipientLastNameC.text = (beneficiary?.lastName ?? "").isNotEmpty
        ? (beneficiary?.lastName ?? "").capitalize()
        : "";

    vm.setMobileMoney(MobileMoney(
      uuid: beneficiary?.institutionCode ?? '',
      name: beneficiary?.institutionName ?? '',
      mobileNumber: beneficiary?.accountIdentifier ?? '',
      recipientName: beneficiary?.accountName ?? '',
    ));

    setState(() {});
  }

  void verifyMobileWalletByCurrecyId() {
    final vm = context.read<SendMoneyVM>();
    vm
        .verifyMobileWalletByCurrecyId(
      currencyId: vm.recipientCurrency?.id ?? 0,
      destinationNumber: mobileNumberC.text,
    )
        .then((v) {
      hasStartedVerification = true;
      isMobileNumberValid = widget.arg.paymentMethod.nameCheck == true
          ? (v.data ?? '').toString().toLowerCase() == 'active'
          : true;
    });
  }
}
