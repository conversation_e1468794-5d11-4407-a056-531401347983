import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class SendBankTransferScreen extends StatefulWidget {
  const SendBankTransferScreen({
    super.key,
    required this.arg,
  });

  /// NOTE: if namecheck is true
  /// Add validation for account name
  /// if  false, do no validation
  final TransferMethodArg arg;

  @override
  State<SendBankTransferScreen> createState() => _SendBankTransferScreenState();
}

class _SendBankTransferScreenState extends State<SendBankTransferScreen> {
  final bankNameC = TextEditingController();
  final accountNumC = TextEditingController();
  final recipientFirstNameC = TextEditingController();
  final recipientLastNameC = TextEditingController();

  Bank? selectedBank;
  String? accountName;
  bool saveBeneficiary = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final sendMoneyVM = context.read<SendMoneyVM>();
      final bankVm = context.read<BankVM>();
      bankVm.getBanksByCurrencyId(sendMoneyVM.recipientCurrency?.id ?? 0);
      _setFirstAndLastNameFromBeneficiary(widget.arg.beneficiary);
    });
  }

  @override
  void dispose() {
    bankNameC.dispose();
    accountNumC.dispose();
    recipientFirstNameC.dispose();
    recipientLastNameC.dispose();
    super.dispose();
  }

  bool get enableButton =>
      bankNameC.text.trim().isNotEmpty &&
      accountNumC.text.trim().isNotEmpty &&
      (widget.arg.paymentMethod.nameCheck == true
          ? accountName != null
          : (recipientFirstNameC.text.isNotEmpty &&
              recipientLastNameC.text.isNotEmpty));

  @override
  Widget build(BuildContext context) {
    final beneficiary = widget.arg.beneficiary;
    return Consumer<SendMoneyVM>(builder: (context, vm, _) {
      // printty(" nameCheck ${widget.arg.paymentMethod.nameCheck}");
      // printty(" accountName ${vm.accountName}");
      return BusyOverlay(
        show: vm.isBusy,
        child: Scaffold(
          appBar: NewCustomAppbar(
            showHeaderTitle: true,
            headerText: 'Send to ${vm.recipientCurrency?.code} Bank Account',
            onBackBtnTap: () {
              vm.setTransferParams(null);

              Navigator.pop(context);
            },
          ),
          body: Column(
            children: [
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      Text(
                        "Please input Account details of your Beneficiary",
                        style: AppTypography.text16
                            .copyWith(color: AppColors.gray93),
                      ),
                      const YBox(40),
                      Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: Sizer.width(24),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            CustomTextField(
                              controller: bankNameC,
                              labelText: "Select Bank",
                              showLabelHeader: true,
                              hintText: 'Access Bank',
                              borderRadius: Sizer.height(12),
                              showSuffixIcon: true,
                              isReadOnly: true,
                              fillColor: AppColors.grayFA,
                              onTap: beneficiary != null
                                  ? null
                                  : () async {
                                      final res = await BsWrapper.bottomSheet(
                                        context: context,
                                        widget: BankSheet(
                                          bankName: bankNameC.text,
                                          selectBankType:
                                              SelectBankType.sendMoney,
                                          recipientCurrencyId:
                                              vm.recipientCurrency?.id,
                                        ),
                                      );

                                      if (res is Bank) {
                                        accountNumC.clear();
                                        selectedBank = res;
                                        bankNameC.text = res.name!;
                                        setState(() {});
                                      }
                                    },
                            ),
                            const YBox(24),
                            CustomTextField(
                              controller: accountNumC,
                              labelText: "Account Number",
                              showLabelHeader: true,
                              isReadOnly: beneficiary != null,
                              fillColor: beneficiary != null
                                  ? AppColors.grayFA
                                  : AppColors.white,
                              keyboardType: KeyboardType.number,
                              inputFormatters: [
                                FilteringTextInputFormatter.digitsOnly,
                                LengthLimitingTextInputFormatter(10),
                              ],
                              hintText: '**********',
                              borderRadius: Sizer.height(12),
                              suffixIcon: vm.busy(verifyingBankState)
                                  ? const CupertinoActivityIndicator()
                                  : null,
                              onChanged: (val) async {
                                if (val.trim().length > 9 &&
                                    widget.arg.paymentMethod.nameCheck ==
                                        true) {
                                  FocusScope.of(context).unfocus();
                                  final res =
                                      await vm.verifyBankAcctByCurrencyId(
                                    currencyId: vm.recipientCurrency?.id ?? 0,
                                    bankId: selectedBank?.uuid ?? "",
                                    accountNum: accountNumC.text,
                                    amount:
                                        vm.recipientC.text.replaceAllCommas(),
                                  );

                                  handleApiResponse(
                                    response: res,
                                    showSuccessToast: false,
                                    errorMsg:
                                        'Unable to verify account, kindly try again',
                                    onSuccess: () {
                                      accountName =
                                          vm.verifiedBank?.accountName;
                                      setState(() {});
                                    },
                                  );
                                }
                              },
                              onTap: () {
                                if (bankNameC.text.isEmpty) {
                                  showWarningToast(
                                      'Please select bank before proceeding');
                                }
                              },
                            ),
                            if ((accountName != null &&
                                    bankNameC.text.isNotEmpty) &&
                                widget.arg.paymentMethod.nameCheck == true)
                              AccountNameWidget(accountName: accountName ?? ""),
                            if (widget.arg.paymentMethod.nameCheck == false)
                              Padding(
                                padding: EdgeInsets.only(
                                  top: Sizer.height(24),
                                ),
                                child: Row(
                                  crossAxisAlignment: CrossAxisAlignment.end,
                                  children: [
                                    Expanded(
                                      child: CustomTextField(
                                          controller: recipientFirstNameC,
                                          labelText: "Recipient",
                                          showLabelHeader: true,
                                          hintText: 'First Name',
                                          borderRadius: Sizer.height(4),
                                          onChanged: (p0) {
                                            AppUtils.handleTextCapitalization(
                                                recipientFirstNameC, p0);
                                            setState(() {});
                                          }),
                                    ),
                                    const XBox(10),
                                    Expanded(
                                      child: CustomTextField(
                                          controller: recipientLastNameC,
                                          hintText: 'Last Name',
                                          borderRadius: Sizer.height(4),
                                          onChanged: (p0) {
                                            AppUtils.handleTextCapitalization(
                                                recipientLastNameC, p0);
                                            setState(() {});
                                          }),
                                    ),
                                  ],
                                ),
                              ),
                          ],
                        ),
                      ),
                      if (beneficiary == null)
                        Padding(
                          padding: EdgeInsets.only(
                            top: Sizer.height(30),
                            left: Sizer.width(24),
                          ),
                          child: Row(
                            children: [
                              CustomSwitch(
                                value: saveBeneficiary,
                                onChanged: (value) {
                                  saveBeneficiary = value;
                                  setState(() {});
                                },
                              ),
                              const XBox(12),
                              Text(
                                "Save beneficiary",
                                style: AppTypography.text16.copyWith(
                                  color: AppColors.gray500,
                                ),
                              ),
                            ],
                          ),
                        ),
                      const YBox(100),
                    ],
                  ),
                ),
              ),
            ],
          ),
          bottomSheet: Container(
            color: AppColors.white,
            padding: EdgeInsets.symmetric(
              horizontal: Sizer.width(24),
            ).copyWith(
              bottom: Sizer.height(30),
            ),
            child: CustomBtn.withChild(
              onTap: () async {
                FocusScope.of(context).unfocus();

                if (beneficiary == null) {
                  vm.setTransferParams(TransferParams(
                    destinationBankUuid: selectedBank?.uuid,
                    bankName: bankNameC.text,
                    accountName: accountName,
                    destinationBankAccountNumber: accountNumC.text,
                    transferMethod: TransferMethod.bankTransfer,
                    saveBeneficiary: saveBeneficiary,
                  ));
                  final result = await BsWrapper.bottomSheet(
                    context: context,
                    widget: BeneficiaryScamSheet(),
                  );

                  if (result is bool && result) {
                    if (widget.arg.paymentMethod.nameCheck == false) {
                      printty("Name was checked");
                      accountName =
                          '${recipientFirstNameC.text.trim()} ${recipientLastNameC.text.trim()}';
                      // vm.setAccountName(
                      //     '${recipientFirstNameC.text.trim()} ${recipientLastNameC.text.trim()}');
                    }

                    Navigator.pushNamed(context, RoutePath.reviewScreen,
                        arguments: SendMoneyReviewsArg(
                          name: accountName ?? "",
                          title: accountName ?? "",
                          iconPath: "",
                          subTitle: "${bankNameC.text} • ${accountNumC.text}",
                        ));
                  }

                  return;
                }

                if (widget.arg.paymentMethod.nameCheck == false) {
                  printty("Name was checked");
                  accountName =
                      '${recipientFirstNameC.text.trim()} ${recipientLastNameC.text.trim()}';
                }
                Navigator.pushNamed(
                  context,
                  RoutePath.reviewScreen,
                  arguments: SendMoneyReviewsArg(
                      name: accountName ?? "",
                      title: accountName ?? "",
                      iconPath: beneficiary.iconUrl ?? "",
                      subTitle:
                          "${beneficiary.institutionName} • ${beneficiary.accountIdentifier}"),
                );
              },
              online: enableButton,
              borderRadius: BorderRadius.circular(Sizer.radius(20)),
              child: ContinueText(isOnline: enableButton),
            ),
          ),
        ),
      );
    });
  }

  _setFirstAndLastNameFromBeneficiary([Beneficiary? beneficiary]) {
    final sendMoneyVM = context.read<SendMoneyVM>();
    if (widget.arg.paymentMethod.nameCheck == false) {
      recipientFirstNameC.text = beneficiary?.firstName ?? "";
      recipientLastNameC.text = beneficiary?.lastName ?? "";
    }

    accountName = beneficiary?.accountName;
    bankNameC.text = beneficiary?.institutionName ?? "";
    accountNumC.text = beneficiary?.accountIdentifier ?? "";
    sendMoneyVM.autoFillAcctNumberName(widget.arg.beneficiary);

    setState(() {});
  }
}
