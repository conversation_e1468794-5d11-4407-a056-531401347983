import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class KorrencyPointScreen extends StatelessWidget {
  const KorrencyPointScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: NewCustomAppbar(
        showHeaderTitle: true,
        headerText: 'Korrency Points',
      ),
      body: ListView(
        padding: EdgeInsets.symmetric(
          horizontal: Sizer.width(32),
          vertical: Sizer.height(30),
        ),
        children: [
          YBox(100),
          SvgPicture.asset(
            AppSvgs.comingSoon,
            height: Sizer.height(150),
          ),
          YBox(30),
          Text(
            'Korrency Points Coming soon!',
            style: AppTypography.text18.semiBold,
            textAlign: TextAlign.center,
          ),
          YBox(12),
          Text(
            'Earn points when you send money to loved ones using Korrency. Get ready to send, earn, and enjoy more with every transfer.',
            style: AppTypography.text14.copyWith(
              color: AppColors.gray808,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
