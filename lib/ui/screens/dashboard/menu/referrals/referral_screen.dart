import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';
import 'package:share_plus/share_plus.dart';

class ReferralScreen extends StatefulWidget {
  const ReferralScreen({super.key});

  @override
  State<ReferralScreen> createState() => _ReferralScreenState();
}

class _ReferralScreenState extends State<ReferralScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  int selectedIndex = 0;

  @override
  void initState() {
    _tabController = TabController(length: 2, vsync: this);
    MixpanelService().trackScreen("Referral Page Viewed");
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _callRefs();
    });

    super.initState();
  }

  _callRefs() {
    context.read<ReferralVM>()
      ..getReferralEarnings()
      ..getReferrals();
  }

  _showRatingPrompt() {
    BsWrapper.bottomSheet(
      context: context,
      widget: const RateExperienceModal(),
    );
  }

  @override
  Widget build(BuildContext context) {
    final referralVm = context.watch<ReferralVM>();
    final authVm = context.watch<AuthUserVM>();
    return BusyOverlay(
      show: referralVm.isBusy,
      child: Scaffold(
        body: Column(
          children: [
            Stack(
              clipBehavior: Clip.none,
              children: [
                Container(
                  height: Sizer.height(200),
                  decoration: BoxDecoration(
                    color: AppColors.blue8FF,
                  ),
                  child: SafeArea(
                    child: Padding(
                      padding: EdgeInsets.symmetric(
                        horizontal: Sizer.width(24),
                      ),
                      child: Column(
                        children: [
                          YBox(20),
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              ArrowBack(),
                              XBox(20),
                              Text(
                                'Track Invites',
                                style: AppTypography.text22.copyWith(
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                Positioned(
                  bottom: -50,
                  left: 0,
                  right: 0,
                  child: Padding(
                    padding: EdgeInsets.symmetric(
                      horizontal: Sizer.width(30),
                    ),
                    child: Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: Sizer.width(28),
                        vertical: Sizer.height(16),
                      ),
                      decoration: BoxDecoration(
                        color: AppColors.white,
                        border: Border.all(
                          color: AppColors.blue5FF,
                        ),
                        borderRadius: BorderRadius.circular(Sizer.radius(6)),
                      ),
                      child: IntrinsicHeight(
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Expanded(
                              child: Center(
                                child: ReferralColText(
                                  title: 'Total Earned',
                                  subtitle:
                                      '\$${referralVm.refEarnings?.bonusesEarned ?? 0}',
                                ),
                              ),
                            ),
                            VerticalDivider(
                              color: AppColors.blue5FF,
                            ),
                            Expanded(
                              child: Center(
                                child: ReferralColText(
                                  title: 'Invited friends',
                                  subtitle: '${referralVm.allRefList.length}',
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
            Expanded(
              child: Padding(
                padding: EdgeInsets.only(
                  left: Sizer.width(30),
                  right: Sizer.width(30),
                  top: Sizer.height(20),
                ),
                child: Column(
                  children: [
                    const YBox(56),
                    Container(
                      height: Sizer.height(42),
                      width: Sizer.screenWidth,
                      decoration: BoxDecoration(
                        color: AppColors.grayFE,
                        borderRadius: BorderRadius.circular(Sizer.radius(60)),
                        border: Border.all(width: 1, color: AppColors.grayF0),
                      ),
                      child: TabBar(
                        // splashBorderRadius: BorderRadius.circular(Sizer.radius(12)),
                        physics: const NeverScrollableScrollPhysics(),
                        onTap: (int value) {
                          selectedIndex = value;
                          setState(() {});
                        },
                        dividerColor: Colors.transparent,
                        indicator: BoxDecoration(
                          borderRadius: BorderRadius.circular(Sizer.radius(60)),
                          color: AppColors.primaryBlue,
                        ),
                        indicatorSize: TabBarIndicatorSize.tab,
                        labelColor: AppColors.white,
                        automaticIndicatorColorAdjustment: true,
                        labelStyle: AppTypography.text14.copyWith(
                          fontWeight: FontWeight.w500,
                        ),
                        unselectedLabelStyle: AppTypography.text14.copyWith(
                          color: AppColors.primaryBlue,
                          fontWeight: FontWeight.w500,
                        ),
                        controller: _tabController,
                        tabs: [
                          Row(
                            children: [
                              SvgPicture.asset(
                                selectedIndex == 0
                                    ? AppSvgs.smsTrackingB
                                    : AppSvgs.smsTracking,
                              ),
                              const XBox(4),
                              Text(
                                'Pending Invites',
                                style: AppTypography.text12.copyWith(
                                  fontWeight: FontWeight.w600,
                                  color: selectedIndex == 0
                                      ? AppColors.white
                                      : AppColors.primaryBlue,
                                ),
                              ),
                            ],
                          ),
                          Row(
                            children: [
                              SvgPicture.asset(
                                selectedIndex == 1
                                    ? AppSvgs.profileTickB
                                    : AppSvgs.profileTick,
                              ),
                              const XBox(4),
                              Text(
                                'Completed Invites',
                                style: AppTypography.text12.copyWith(
                                  fontWeight: FontWeight.w600,
                                  color: selectedIndex == 1
                                      ? AppColors.white
                                      : AppColors.primaryBlue,
                                ),
                              ),
                            ],
                          )
                        ],
                      ),
                    ),
                    Expanded(
                      child: RefreshIndicator(
                        onRefresh: () async {
                          // _callRefs();
                        },
                        child: ListView(
                          padding: EdgeInsets.zero,
                          children: [
                            // DottedBorder(
                            //   dashPattern: const [8, 5],
                            //   strokeWidth: 2,
                            //   borderType: BorderType.RRect,
                            //   radius: const Radius.circular(4),
                            //   color: AppColors.dottedColor,
                            //   // padding: EdgeInsets.symmetric(
                            //   //   horizontal: Sizer.width(16),
                            //   //   vertical: Sizer.height(18),
                            //   // ),
                            //   child: SizedBox(
                            //     width: Sizer.screenWidth,
                            //     child: Column(
                            //       children: [
                            //         InkWell(
                            //           onTap: () async {
                            //             if (referralVm.refEarnings
                            //                     ?.showEnabledClaimBonusesButton ??
                            //                 false) {
                            //               printty("claimRefBonus called");
                            //               final res = await referralVm.claimRefBonus();

                            //               handleApiResponse(
                            //                 response: res,
                            //                 onSuccess: () async {
                            //                   _callRefs();
                            //                   final res = await context
                            //                       .read<RatingVm>()
                            //                       .getEligibility();
                            //                   if (res.success) {
                            //                     if (context
                            //                             .read<RatingVm>()
                            //                             .eligibilityModel
                            //                             ?.eligibility ??
                            //                         false) {
                            //                       _showRatingPrompt();
                            //                     }
                            //                   }
                            //                 },
                            //               );
                            //             }
                            //             return;
                            //           },
                            //           child: Container(
                            //             width: Sizer.screenWidth,
                            //             padding: EdgeInsets.symmetric(
                            //               horizontal: Sizer.width(16),
                            //               vertical: Sizer.width(10),
                            //             ),
                            //             decoration: BoxDecoration(
                            //                 color: AppColors.dividerColor,
                            //                 borderRadius: BorderRadius.circular(4)),
                            //             child: Row(
                            //               mainAxisAlignment: MainAxisAlignment.center,
                            //               children: [
                            //                 Text(
                            //                   (referralVm.refEarnings?.showClaimedButton ??
                            //                           false)
                            //                       ? "Claimed"
                            //                       : "Claim Bonuses",
                            //                   style: AppTypography.text16.copyWith(
                            //                       fontWeight: FontWeight.w500,
                            //                       color: (referralVm.refEarnings
                            //                                   ?.showClaimedButton ??
                            //                               false)
                            //                           ? AppColors.iconGreen
                            //                           : AppColors.baseBlack),
                            //                 ),
                            //                 if (referralVm.refEarnings?.showClaimedButton ??
                            //                     false)
                            //                   const XBox(6),
                            //                 if (referralVm.refEarnings?.showClaimedButton ??
                            //                     false)
                            //                   const Icon(
                            //                     Icons.check_circle,
                            //                     size: 20,
                            //                     color: AppColors.iconGreen,
                            //                   ),
                            //               ],
                            //             ),
                            //           ),
                            //         ),
                            //       ],
                            //     ),
                            //   ),
                            // ),

                            // CustomBtn.solid(
                            //   onTap: () {
                            //     printty("claimRefBonus called");
                            //     vm.claimRefBonus().then((value) {
                            //       if (value.success) {
                            //         FlushBarToast.fLSnackBar(
                            //           message: value.message.toString(),
                            //           backgroundColor: AppColors.baseGreen,
                            //         );
                            //       } else {
                            //         FlushBarToast.fLSnackBar(
                            //           message: value.message.toString(),
                            //           backgroundColor: AppColors.red,
                            //         );
                            //       }
                            //     });
                            //   },
                            //   online: false,
                            //   height: 44,
                            //   text: "Claim Bonuses",
                            // ),
                            const YBox(30),
                            if (selectedIndex == 0)
                              Builder(builder: (context) {
                                if (referralVm.pendingRefList.isEmpty) {
                                  return Column(
                                    children: [
                                      SizedBox(
                                        height: Sizer.height(300),
                                        child: Center(
                                          child: EmptyState(
                                            title: "No Invites yet",
                                            subtitle:
                                                "You haven’t sent out any invites yet, Click the button below to share your referral code with friends",
                                          ),
                                        ),
                                      ),
                                      YBox(40),
                                      CustomBtn.solid(
                                        onTap: () async {
                                          var configVM =
                                              context.read<ConfigVM>();
                                          await Share.share(
                                            referalShare(
                                              refCode:
                                                  authVm.user?.referralCode,
                                              refLink:
                                                  'https://korrency.onelink.me/9BIc?referrer=${authVm.user?.referralCode}',
                                              refBonusAmt:
                                                  configVM.referralBonusAmount,
                                              refamtForReferral:
                                                  configVM.amtForReferral,
                                            ),
                                            subject: "Korrency",
                                          );

                                          MixpanelService().track(
                                              'Referral Code Shared',
                                              properties: {
                                                "referral_code":
                                                    authVm.user?.referralCode,
                                                "referral_link":
                                                    'https://korrency.onelink.me/9BIc?referrer=${authVm.user?.referralCode}',
                                                "referral_amount": configVM
                                                    .referralBonusAmount,
                                                "time": DateTime.now()
                                                    .toIso8601String()
                                              });
                                        },
                                        borderRadius: BorderRadius.circular(
                                            Sizer.radius(20)),
                                        text: "Share",
                                      ),
                                    ],
                                  );
                                }
                                return ListView.separated(
                                  shrinkWrap: true,
                                  physics: const NeverScrollableScrollPhysics(),
                                  padding: EdgeInsets.zero,
                                  itemBuilder: (ctx, i) {
                                    Referral pendingRef =
                                        referralVm.pendingRefList[i];
                                    return RefListTile(
                                      name:
                                          pendingRef.referredUserFullName ?? '',
                                      isClaimed: false,
                                      status: pendingRef.status ?? '',
                                      onTap: () {},
                                    );
                                  },
                                  separatorBuilder: (_, __) => const YBox(30),
                                  itemCount: referralVm.pendingRefList.length,
                                );
                              }),
                            if (selectedIndex == 1)
                              Builder(builder: (context) {
                                if (referralVm.completedRefList.isEmpty) {
                                  return Column(
                                    children: [
                                      SizedBox(
                                        height: Sizer.height(300),
                                        child: Center(
                                          child: EmptyState(
                                            title: "No Invites yet",
                                            subtitle:
                                                "You haven’t sent out any invites yet, Click the button below to share your referral code with friends",
                                          ),
                                        ),
                                      ),
                                      YBox(40),
                                      CustomBtn.solid(
                                        onTap: () async {
                                          var configVM =
                                              context.read<ConfigVM>();
                                          await Share.share(
                                            referalShare(
                                              refCode:
                                                  authVm.user?.referralCode,
                                              refLink:
                                                  'https://korrency.onelink.me/9BIc?referrer=${authVm.user?.referralCode}',
                                              refBonusAmt:
                                                  configVM.referralBonusAmount,
                                              refamtForReferral:
                                                  configVM.amtForReferral,
                                            ),
                                            subject: "Korrency",
                                          );

                                          MixpanelService().track(
                                              'Referral Code Shared',
                                              properties: {
                                                "referral_code":
                                                    authVm.user?.referralCode,
                                                "referral_link":
                                                    'https://korrency.onelink.me/9BIc?referrer=${authVm.user?.referralCode}',
                                                "referral_amount": configVM
                                                    .referralBonusAmount,
                                                "time": DateTime.now()
                                                    .toIso8601String()
                                              });
                                        },
                                        borderRadius: BorderRadius.circular(
                                            Sizer.radius(20)),
                                        text: "Share",
                                      ),
                                    ],
                                  );
                                }
                                return ListView.separated(
                                  shrinkWrap: true,
                                  padding: EdgeInsets.zero,
                                  physics: const NeverScrollableScrollPhysics(),
                                  itemBuilder: (ctx, i) {
                                    Referral claimedRef =
                                        referralVm.completedRefList[i];
                                    return RefListTile(
                                      name:
                                          claimedRef.referredUserFullName ?? '',
                                      status: claimedRef.status ?? '',
                                      isClaimed: true,
                                      onTap: () {},
                                    );
                                  },
                                  separatorBuilder: (_, __) => const YBox(30),
                                  itemCount: referralVm.completedRefList.length,
                                );
                              }),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class EmptyReferrals extends StatelessWidget {
  const EmptyReferrals({
    super.key,
    this.title,
  });

  final String? title;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: Sizer.height(300),
      child: Center(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            imageHelper(
              AppImages.phoneUser,
              height: Sizer.height(236),
            ),
            Text(
              title ?? "Invite others to join the platform",
              style: AppTypography.text16.copyWith(
                fontWeight: FontWeight.w400,
                color: AppColors.textBlack600,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
