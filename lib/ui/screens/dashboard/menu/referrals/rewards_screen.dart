import 'package:korrency/core/core.dart';

class RewardsScreen extends StatelessWidget {
  const RewardsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        automaticallyImplyLeading: false,
        centerTitle: false,
        elevation: 0,
        toolbarHeight: 60,
        backgroundColor: AppColors.white,
        title: Padding(
          padding: EdgeInsets.symmetric(
            horizontal: Sizer.width(8),
          ),
          child: Text(
            'Rewards',
            style: AppTypography.text22.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ),
      body: ListView(
        padding: EdgeInsets.symmetric(
          horizontal: Sizer.width(24),
          vertical: Sizer.height(30),
        ),
        children: [
          Row(
            children: [
              Expanded(
                child: RewardCard(
                  title: 'Referrals',
                  subTitle: 'Monitor your referral\nearnings',
                  svgIcon: AppSvgs.mediaDiscussion,
                  onTap: () {
                    Navigator.pushNamed(context, RoutePath.referralScreen);
                  },
                ),
              ),
              XBox(24),
              Expanded(
                child: RewardCard(
                  title: 'Korrency Points',
                  subTitle: 'Track your reward \npoints',
                  svgIcon: AppSvgs.cardlessWithdrawal,
                  bgColor: AppColors.yellow77,
                  borderColor: AppColors.yellowC3,
                  onTap: () {
                    Navigator.pushNamed(context, RoutePath.korrencyPointScreen);
                  },
                ),
              ),
            ],
          )
        ],
      ),
    );
  }
}

class RewardCard extends StatelessWidget {
  const RewardCard({
    super.key,
    required this.title,
    required this.subTitle,
    required this.svgIcon,
    this.bgColor = AppColors.purpleFF,
    this.borderColor = AppColors.purple1FF,
    this.onTap,
  });

  final String title;
  final String subTitle;
  final String svgIcon;
  final Color bgColor;
  final Color borderColor;
  final Function()? onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: Sizer.width(16),
          vertical: Sizer.height(12),
        ),
        decoration: BoxDecoration(
          color: bgColor,
          border: Border.all(
            color: borderColor,
            width: 2,
          ),
          borderRadius: BorderRadius.circular(Sizer.radius(8)),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: AppTypography.text16.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            YBox(4),
            Text(
              subTitle,
              style: AppTypography.text12.copyWith(
                color: AppColors.gray51,
                height: 1.3,
              ),
            ),
            YBox(16),
            SvgPicture.asset(
              svgIcon,
              height: Sizer.height(130),
            ),
          ],
        ),
      ),
    );
  }
}
