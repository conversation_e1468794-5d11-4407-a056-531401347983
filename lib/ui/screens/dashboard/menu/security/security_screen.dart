// ignore_for_file: use_build_context_synchronously

import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class SecurityScreen extends StatefulWidget {
  const SecurityScreen({super.key});

  @override
  State<SecurityScreen> createState() => _SecurityScreenState();
}

class _SecurityScreenState extends State<SecurityScreen> {
  // final bool _isActive = false;
  bool _useFaceId = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      StorageService.getBoolItem(StorageKey.fingerPrintIsEnabled).then((value) {
        setState(() {
          _useFaceId = value ?? false;
        });
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.white,
      appBar: NewCustomAppbar(
        showHeaderTitle: true,
        headerText: 'Privacy and Security',
      ),
      body: Container(
        padding: EdgeInsets.symmetric(
          horizontal: Sizer.width(24),
        ).copyWith(
          top: Sizer.height(10),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              "Manage all your privacy and security settings below.",
              style: FontTypography.text16.withCustomColor(AppColors.gray93),
            ),
            Expanded(
              child: Container(
                width: Sizer.screenWidth,
                color: AppColors.white,
                child: ListView(
                  padding: EdgeInsets.only(
                    top: Sizer.height(40),
                    bottom: Sizer.height(24),
                  ),
                  children: [
                    CustomMenuItem(
                      title: 'Change Transaction PIN',
                      icon: Iconsax.user_edit,
                      iconColor: AppColors.secondaryBlue,
                      showTrailing: false,
                      onPressed: () {
                        Navigator.pushNamed(
                          context,
                          RoutePath.answerQuestionScreen,
                        );
                      },
                    ),
                    const YBox(30),
                    CustomMenuItem(
                      title: 'Change Email',
                      icon: Iconsax.sms_edit,
                      iconColor: AppColors.secondaryBlue,
                      showTrailing: false,
                      onPressed: () async {
                        final res = await BsWrapper.showCustomDialog(
                          context,
                          child: const PasswordInputDialog(),
                        );

                        if (res is bool && res) {
                          Navigator.pushNamed(
                            context,
                            RoutePath.changeEmailPhoneScreen,
                            arguments: ChangeInfoArg(
                              recipient: "",
                              isPhone: false,
                            ),
                          );
                        }
                      },
                    ),
                    const YBox(30),
                    CustomMenuItem(
                      title: 'Change Phone Number',
                      icon: Iconsax.call,
                      showTrailing: false,
                      iconColor: AppColors.secondaryBlue,
                      onPressed: () async {
                        final res = await BsWrapper.showCustomDialog(
                          context,
                          child: const PasswordInputDialog(),
                        );

                        if (res is bool && res) {
                          Navigator.pushNamed(
                            context,
                            RoutePath.changeEmailPhoneScreen,
                            arguments: ChangeInfoArg(
                              recipient: "",
                              isPhone: true,
                            ),
                          );
                        }
                      },
                    ),
                    const YBox(30),
                    CustomMenuItem(
                      title: 'Unlock with Biometrics',
                      icon: Iconsax.finger_cricle,
                      iconColor: AppColors.secondaryBlue,
                      showTrailing: true,
                      trailingWidget: CustomSwitch(
                        value: _useFaceId,
                        onChanged: updateFingerPrint,
                      ),
                    ),
                    const YBox(30),
                    CustomMenuItem(
                      title: 'Change Password',
                      icon: Iconsax.lock_1,
                      iconColor: AppColors.secondaryBlue,
                      showTrailing: false,
                      onPressed: () {
                        Navigator.pushNamed(
                          context,
                          RoutePath.newPasswordScreen,
                        );
                      },
                    ),
                    const YBox(30),
                    CustomMenuItem(
                      title: 'Trusted Device',
                      icon: AppSvgs.phone1,
                      iconColor: AppColors.secondaryBlue,
                      showTrailing: false,
                      onPressed: () {
                        Navigator.pushNamed(
                          context,
                          RoutePath.myDeviceScreen,
                        );
                      },
                    ),
                    const YBox(30),
                    CustomMenuItem(
                      title: '2FA',
                      icon: AppSvgs.ffa,
                      iconColor: AppColors.secondaryBlue,
                      showTrailing: false,
                      onPressed: () {
                        Navigator.pushNamed(
                          context,
                          RoutePath.twoFactorAuthScreen,
                        );
                      },
                    ),
                    // const YBox(20),
                    // CustomMenuItem(
                    //   title: 'Security Question',
                    //   useImageIcon: true,
                    //   isSvg: true,
                    //   imageIcon: AppSvgs.ffa,
                    //   onPressed: () {
                    //     Navigator.pushNamed(
                    //       context,
                    //       RoutePath.securityQuestionScreen,
                    //     );
                    //   },
                    // ),
                    // const YBox(20),
                    // CustomMenuItem(
                    //   title: 'Create Transaction PIN',
                    //   useImageIcon: true,
                    //   isSvg: true,
                    //   imageIcon: AppSvgs.ffa,
                    //   onPressed: () {
                    //     BsWrapper.bottomSheet(
                    //         context: context, widget: const SetupPinScreen());
                    //   },
                    // ),
                    // const YBox(20),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  updateFingerPrint(bool val) async {
    if (val) {
      bool isAuthenticated = await BiometricService.authenticate();
      if (isAuthenticated) {
        StorageService.storeBoolItem(StorageKey.fingerPrintIsEnabled, val);
        setState(() {
          _useFaceId = val;
        });
      } else {
        FlushBarToast.fLSnackBar(
          message: "Biometric Authentication Failed",
        );
      }
    } else {
      await StorageService.removeBoolItem(StorageKey.fingerPrintIsEnabled);
      setState(() {
        _useFaceId = val;
      });
    }
  }
}
