import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class AccountSettingScreen extends StatelessWidget {
  const AccountSettingScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: NewCustomAppbar(
        showHeaderTitle: true,
        headerText: 'Account Settings',
      ),
      body: Container(
        padding: EdgeInsets.symmetric(
          horizontal: Sizer.width(24),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              "Manage all details for your account here",
              style: AppTypography.text16.copyWith(color: AppColors.gray93),
            ),
            const YBox(40),
            BuildSettingRowWidget(
              text: 'Account Limits',
              svgIcon: AppSvgs.sendSquare,
              onTap: () async {
                final res = await BsWrapper.bottomSheet(
                  context: context,
                  widget: SelectWalletModal(
                    exportWallet: true,
                  ),
                );

                if (res is Wallet) {
                  if (context.mounted) {
                    Navigator.pushNamed(
                      context,
                      RoutePath.accountLimitScreen,
                      arguments: res.currency,
                    );
                  }
                }
              },
            ),
            const YBox(30),
            BuildSettingRowWidget(
              text: 'Account Statement',
              svgIcon: AppSvgs.documentText,
              onTap: () {
                Navigator.pushNamed(context, RoutePath.accountStatementScreen);
              },
            ),
            const YBox(30),
            // BuildSettingRowWidget(
            //   text: 'Manage Wallets',
            //   svgIcon: AppSvgs.driver,
            //   onTap: () {},
            // ),
            Spacer(),
            BuildSettingRowWidget(
              text: 'Deactivate Account',
              svgIcon: AppSvgs.trashSquare,
              bgColor: AppColors.redF5,
              onTap: () {
                Navigator.pushNamed(context, RoutePath.deactivateAccountScreen);
              },
            ),
            const YBox(100),
          ],
        ),
      ),
    );
  }
}

class BuildSettingRowWidget extends StatelessWidget {
  const BuildSettingRowWidget({
    super.key,
    required this.text,
    required this.svgIcon,
    this.bgColor,
    required this.onTap,
  });

  final String text;
  final String svgIcon;
  final Color? bgColor;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(Sizer.radius(10)),
            decoration: BoxDecoration(
              color: bgColor ?? AppColors.grayFE,
              borderRadius: BorderRadius.circular(30),
            ),
            child: SvgPicture.asset(svgIcon),
          ),
          const XBox(12),
          Text(
            text,
            style: AppTypography.text16.copyWith(
              color: AppColors.black22,
            ),
          ),
        ],
      ),
    );
  }
}
