import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  final firstNameController = TextEditingController();
  final lastNameController = TextEditingController();
  final middleNameController = TextEditingController();
  final phoneController = TextEditingController();
  final emailController = TextEditingController();
  final genderController = TextEditingController();
  final korrencyUsernameController = TextEditingController();
  final dateOfBirthController = TextEditingController();
  final occupationController = TextEditingController();
  final countryController = TextEditingController();
  final addressController = TextEditingController();
  final stateController = TextEditingController();
  final cityController = TextEditingController();
  final postalCodeController = TextEditingController();

  OccupationData? selectedOccupation;
  bool _showListSelector = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<KycVM>().getOccupationData();
      final authUserVM = context.read<AuthUserVM>();
      _updateControllers(authUserVM.user);
    });
  }

  @override
  void dispose() {
    firstNameController.dispose();
    lastNameController.dispose();
    middleNameController.dispose();
    phoneController.dispose();
    emailController.dispose();
    korrencyUsernameController.dispose();
    genderController.dispose();
    dateOfBirthController.dispose();
    occupationController.dispose();
    countryController.dispose();
    addressController.dispose();
    stateController.dispose();
    cityController.dispose();
    postalCodeController.dispose();
    super.dispose();
  }

  // Store original values for comparison
  String? originalOccupation;
  String? originalAddress;
  String? originalCity;
  String? originalState;
  String? originalPostalCode;
  int? originalOccupationId;

  void _updateControllers(AuthUser? user) {
    firstNameController.text = user?.firstName ?? '';
    lastNameController.text = user?.lastName ?? '';
    middleNameController.text = user?.middleName ?? '';
    phoneController.text = user?.phone ?? '';
    emailController.text = user?.email ?? '';
    korrencyUsernameController.text = user?.userName ?? '';
    genderController.text = user?.gender ?? '';
    dateOfBirthController.text = user?.dateOfBirth ?? '';
    occupationController.text = user?.occupation?.name ?? '';
    countryController.text = user?.country ?? '';
    addressController.text = user?.address ?? '';
    stateController.text = user?.state ?? '';
    cityController.text = user?.city ?? '';
    postalCodeController.text = user?.postalCode ?? '';

    // Store original values for modification checking
    originalOccupation = user?.occupation?.name ?? '';
    originalAddress = user?.address ?? '';
    originalCity = user?.city ?? '';
    originalState = user?.state ?? '';
    originalPostalCode = user?.postalCode ?? '';
    originalOccupationId = user?.occupation?.id ?? 0;
  }

  bool _hasModifications() {
    return (selectedOccupation?.id ?? originalOccupationId) !=
            originalOccupationId ||
        addressController.text != originalAddress ||
        cityController.text != originalCity ||
        stateController.text != originalState ||
        postalCodeController.text != originalPostalCode;
  }

  @override
  Widget build(BuildContext context) {
    final authUserVM = context.watch<AuthUserVM>();
    final kycVm = context.watch<KycVM>();
    final suggestionVm = context.watch<AddressSuggestionVM>();
    return Scaffold(
      appBar: NewCustomAppbar(
        showHeaderTitle: true,
        headerText: 'Profile Details',
      ),
      body: ListView(
        padding: EdgeInsets.symmetric(horizontal: Sizer.width(24)),
        children: [
          // InkWell(
          //   onTap: () {
          //     Navigator.pushNamed(
          //         context, RoutePath.createFreshDeskTicketWebview,
          //         arguments: WebViewArg(
          //           webURL: AppUtils.korrencyCreateTicket,
          //         ));
          //   },
          //   child: ContainerWithBluewishBg(
          //     child: Row(
          //       children: [
          //         Icon(
          //           Iconsax.info_circle5,
          //           color: AppColors.primaryBlue,
          //           size: Sizer.radius(24),
          //         ),
          //         const XBox(10),
          //         Expanded(
          //           child: RichText(
          //             text: const TextSpan(children: [
          //               TextSpan(
          //                 text:
          //                     "Profile editing is locked as your account is verified. Please contact",
          //                 style: TextStyle(
          //                   fontSize: 12,
          //                   color: AppColors.textBlack800,
          //                   fontFamily: 'Inter',
          //                 ),
          //               ),
          //               TextSpan(
          //                 text: " customer support",
          //                 style: TextStyle(
          //                   fontSize: 12,
          //                   color: AppColors.textBlue700,
          //                   fontWeight: FontWeight.w500,
          //                   fontFamily: 'Inter',
          //                 ),
          //               ),
          //               TextSpan(
          //                 text: " for changes",
          //                 style: TextStyle(
          //                   fontSize: 12,
          //                   color: AppColors.textBlack800,
          //                   fontFamily: 'Inter',
          //                 ),
          //               ),
          //             ]),
          //           ),
          //         ),
          //       ],
          //     ),
          //   ),
          // ),
          YBox(20),
          Row(
            children: [
              InkWell(
                onTap: () async {
                  final res = await BsWrapper.bottomSheet(
                      context: context, widget: ImageUploadModal());

                  if (res == ImageUploadType.avatar) {
                    if (context.mounted) {
                      final res = await BsWrapper.bottomSheet(
                        context: context,
                        widget: SelectAvatarModal(),
                      );

                      if (res is String && res.isNotEmpty) {
                        final response =
                            await authUserVM.updateProfileAvatar(res);

                        handleApiResponse(
                          response: response,
                          onSuccess: () {
                            context.read<AuthUserVM>().getAuthUser();
                          },
                        );
                      }
                    }
                  } else if (res == ImageUploadType.gallary) {
                    // TODO: Pick from gallery
                  }
                },
                child: Stack(
                  children: [
                    UserAvatar(
                      avatarUrl: authUserVM.user?.avatarUrl ?? "",
                      size: Sizer.radius(80),
                    ),
                    Positioned(
                      bottom: 0,
                      right: 0,
                      child: Container(
                        padding: EdgeInsets.all(Sizer.radius(6)),
                        decoration: BoxDecoration(
                          color: AppColors.primaryBlue,
                          borderRadius: BorderRadius.circular(100),
                        ),
                        child: SvgPicture.asset(
                          AppSvgs.pen,
                          height: Sizer.height(12),
                        ),
                      ),
                    )
                  ],
                ),
              ),
            ],
          ),
          const YBox(20),
          CustomTextField(
            labelText: "First Name",
            controller: firstNameController,
            isReadOnly: true,
            fillColor: AppColors.grayFA,
            showLabelHeader: true,
            borderRadius: Sizer.height(12),
            showSuffixIcon: true,
            suffixIcon: Icon(
              Iconsax.lock_1,
              color: AppColors.grayAB,
              size: Sizer.height(16),
            ),
            onChanged: (val) {},
          ),
          const YBox(20),
          CustomTextField(
            labelText: "Last Name",
            controller: lastNameController,
            isReadOnly: true,
            fillColor: AppColors.grayFA,
            showLabelHeader: true,
            borderRadius: Sizer.height(12),
            showSuffixIcon: true,
            suffixIcon: Icon(
              Iconsax.lock_1,
              color: AppColors.grayAB,
              size: Sizer.height(16),
            ),
            onChanged: (val) {},
          ),
          const YBox(20),
          CustomTextField(
            labelText: "Middle Name (optional)",
            controller: middleNameController,
            isReadOnly: true,
            fillColor: AppColors.grayFA,
            showLabelHeader: true,
            borderRadius: Sizer.height(12),
            showSuffixIcon: true,
            suffixIcon: Icon(
              Iconsax.lock_1,
              color: AppColors.grayAB,
              size: Sizer.height(16),
            ),
            onChanged: (val) {},
          ),
          const YBox(20),
          CustomTextField(
            labelText: "Phone",
            controller: phoneController,
            isReadOnly: true,
            fillColor: AppColors.grayFA,
            showLabelHeader: true,
            borderRadius: Sizer.height(12),
            showSuffixIcon: true,
            suffixIcon: Icon(
              Iconsax.lock_1,
              color: AppColors.grayAB,
              size: Sizer.height(16),
            ),
            onChanged: (val) {},
          ),
          const YBox(20),
          CustomTextField(
            labelText: "Email",
            controller: emailController,
            isReadOnly: true,
            fillColor: AppColors.grayFA,
            showLabelHeader: true,
            borderRadius: Sizer.height(12),
            showSuffixIcon: true,
            suffixIcon: Icon(
              Iconsax.lock_1,
              color: AppColors.grayAB,
              size: Sizer.height(16),
            ),
            onChanged: (val) {},
          ),
          // const YBox(20),
          // CustomTextField(
          //   labelText: "Interac Email",
          //   isReadOnly: true,
          //   fillColor: AppColors.grayFA,
          //   showLabelHeader: true,
          //    borderRadius: Sizer.height(12),
          //   hintText: authUserVM.user?.interacEmail,
          //   showSuffixIcon: true,
          //   suffixIcon: Icon(
          //     Iconsax.lock_1,
          //     color: AppColors.grayAB,
          //     size: Sizer.height(16),
          //   ),
          //   onChanged: (val) {},
          // ),
          const YBox(20),
          CustomTextField(
            labelText: "Korrency Username",
            controller: korrencyUsernameController,
            isReadOnly: true,
            fillColor: AppColors.grayFA,
            showLabelHeader: true,
            borderRadius: Sizer.height(12),
            suffixIcon: Icon(
              Iconsax.lock_1,
              color: AppColors.grayAB,
              size: Sizer.height(16),
            ),
            onChanged: (val) {},
          ),
          const YBox(20),
          CustomTextField(
            labelText: "Gender",
            controller: genderController,
            isReadOnly: true,
            fillColor: AppColors.grayFA,
            showLabelHeader: true,
            borderRadius: Sizer.height(12),
            suffixIcon: Icon(
              Iconsax.lock_1,
              color: AppColors.grayAB,
              size: Sizer.height(16),
            ),
            onChanged: (val) {},
          ),
          const YBox(20),
          CustomTextField(
            labelText: "Date of Birth",
            controller: dateOfBirthController,
            isReadOnly: true,
            fillColor: AppColors.grayFA,
            showLabelHeader: true,
            borderRadius: Sizer.height(12),
            suffixIcon: Icon(
              Iconsax.lock_1,
              color: AppColors.grayAB,
              size: Sizer.height(16),
            ),
            onChanged: (val) {},
          ),
          const YBox(20),
          CustomTextField(
            labelText: "Occupation",
            controller: occupationController,
            isReadOnly: true,
            fillColor: AppColors.blueFE,
            borderColor: AppColors.blueDF2,
            showLabelHeader: true,
            borderRadius: Sizer.height(12),
            showSuffixIcon: true,
            suffixIcon: Icon(
              Iconsax.edit,
              color: AppColors.blueDF2,
              size: Sizer.height(16),
            ),
            onChanged: (val) {},
            onTap: () {
              _showListSelector = true;
              setState(() {});
            },
          ),
          AnimatedSize(
            duration: const Duration(milliseconds: 500),
            child: !_showListSelector
                ? SizedBox.shrink()
                : CustomListSelector<OccupationData>(
                    items: kycVm.occupationData,
                    displayText: (occupation) => occupation.name ?? '',
                    selectedValue: selectedOccupation,
                    onChanged: (occupation) {
                      selectedOccupation = occupation;
                      occupationController.text = occupation?.name ?? '';
                      _showListSelector = false;
                      setState(() {});
                    },
                  ),
          ),
          const YBox(20),
          CustomTextField(
            labelText: "Country of Residence",
            controller: countryController,
            showLabelHeader: true,
            fillColor: AppColors.grayFA,
            isReadOnly: true,
            borderRadius: Sizer.height(12),
            prefixIcon: Container(
              padding: EdgeInsets.all(Sizer.radius(10)),
              child: imageHelper(
                AppImages.cadFlag,
                height: Sizer.height(14),
                width: Sizer.width(20),
              ),
            ),
            suffixIcon: Icon(
              Iconsax.lock_1,
              color: AppColors.grayAB,
              size: Sizer.height(16),
            ),
            onChanged: (val) {},
          ),
          const YBox(20),
          CustomTextField(
            labelText: "Address",
            controller: addressController,
            // isReadOnly: true,
            fillColor: AppColors.blueFE,
            borderColor: AppColors.blueDF2,
            showLabelHeader: true,
            borderRadius: Sizer.height(12),
            suffixIcon: Icon(
              Iconsax.edit,
              color: AppColors.blueDF2,
              size: Sizer.height(16),
            ),
            onChanged: (val) async {
              if (val.isNotEmpty) {
                await context.read<AddressSuggestionVM>().getPlacePredictions(
                    val,
                    countryValue: CountryModel.getCountryByName(
                            authUserVM.user?.country ?? "")
                        ?.countryValue);
              } else {
                suggestionVm.updatePredictions([]);
              }
              setState(() {}); // Trigger UI update to reflect changes
            },
          ),
          Builder(
            builder: (context) {
              if (suggestionVm.predictionViewState == ViewState.busy) {
                return SizedBox(
                  height: Sizer.height(100),
                  child: const Center(
                    child: CircularProgressIndicator(),
                  ),
                );
              }
              if (suggestionVm.predictionViewState == ViewState.error) {
                return const SizedBox.shrink();
              }

              if (suggestionVm.predictions.isEmpty) {
                return const SizedBox.shrink();
              }

              return Container(
                decoration: BoxDecoration(
                  color: AppColors.white,
                  borderRadius: BorderRadius.circular(Sizer.radius(12)),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black12.withValues(alpha: 0.1),
                      blurRadius: 20,
                      offset: const Offset(0, 5),
                    ),
                  ],
                ),
                padding: EdgeInsets.symmetric(
                  vertical: Sizer.height(20),
                  horizontal: Sizer.width(16),
                ),
                height: Sizer.height(250),
                child: ListView.separated(
                  shrinkWrap: true,
                  separatorBuilder: (context, index) => YBox(8),
                  itemCount: suggestionVm.predictions.length,
                  itemBuilder: (context, index) {
                    printty(suggestionVm.predictions.length,
                        level: "from listView");
                    var prediction = suggestionVm.predictions[index];
                    return InkWell(
                      onTap: () async {
                        final currentContext = context;
                        addressController.text =
                            suggestionVm.predictions[index].description ?? '';
                        if (prediction.placeId != null) {
                          final placeDetails = await suggestionVm
                              .getPlaceDetails(prediction.placeId!);
                          printty("placeDetails: ${prediction.placeId}",
                              level: "placeDetails");

                          if (placeDetails.success &&
                              placeDetails.data is List<AddressComponent>) {
                            final addressC =
                                placeDetails.data as List<AddressComponent>;
                            cityController.text = addressC
                                    .firstWhere(
                                      (component) => (component.types ?? [])
                                          .contains('locality'),
                                      orElse: () => AddressComponent(
                                          types: [],
                                          longName: '',
                                          shortName: ''),
                                    )
                                    .longName ??
                                '';
                            stateController.text = addressC
                                    .firstWhere(
                                      (component) => (component.types ?? [])
                                          .contains(
                                              'administrative_area_level_1'),
                                      orElse: () => AddressComponent(
                                          types: [],
                                          longName: '',
                                          shortName: ''),
                                    )
                                    .longName ??
                                '';
                            postalCodeController.text = addressC
                                    .firstWhere(
                                      (component) => (component.types ?? [])
                                          .contains('postal_code'),
                                      orElse: () => AddressComponent(
                                          types: [],
                                          longName: '',
                                          shortName: ''),
                                    )
                                    .longName ??
                                '';
                          }
                          suggestionVm.updatePredictions([]);
                          if (mounted) setState(() {});
                        }
                        if (mounted) {
                          FocusScope.of(currentContext).unfocus();
                        }
                      },
                      child: Padding(
                        padding: EdgeInsets.symmetric(
                          vertical: Sizer.height(8),
                        ),
                        child: Text(
                          prediction.description ?? "",
                          style: AppTypography.text14.copyWith(
                            color: AppColors.gray600,
                          ),
                        ),
                      ),
                    );
                  },
                ),
              );
            },
          ),
          const YBox(20),
          CustomTextField(
            labelText: "State/Region/Province",
            controller: stateController,
            fillColor: AppColors.grayFA,
            isReadOnly: true,
            showLabelHeader: true,
            borderRadius: Sizer.height(12),
            onChanged: (val) {},
          ),
          const YBox(20),
          CustomTextField(
            labelText: "City",
            controller: cityController,
            isReadOnly: true,
            fillColor: AppColors.grayFA,
            showLabelHeader: true,
            borderRadius: Sizer.height(12),
            onChanged: (val) {},
          ),
          const YBox(20),
          CustomTextField(
            labelText: "Postal Code",
            controller: postalCodeController,
            isReadOnly: true,
            fillColor: AppColors.grayFA,
            showLabelHeader: true,
            borderRadius: Sizer.height(12),
            onChanged: (val) {},
          ),
          const YBox(30),
          CustomBtn.solid(
            onTap: _hasModifications() ? _submitProfile : null,
            online: _hasModifications(),
            borderRadius: BorderRadius.circular(Sizer.radius(20)),
            text: 'Save Changes',
          ),
          const YBox(80),
        ],
      ),
    );
  }

  _submitProfile() async {
    // Check if any modifications have been made
    if (!_hasModifications()) {
      // Show message that no changes were made
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('No changes detected to save'),
        ),
      );
      return;
    }

    final kycVm = context.read<KycVM>();

    final res = await kycVm.updateProfile(
      occupationId: selectedOccupation?.id ?? originalOccupationId ?? 0,
      address: addressController.text,
      city: cityController.text,
      state: stateController.text,
      postalCode: postalCodeController.text,
    );

    handleApiResponse(
      response: res,
      onSuccess: () {
        context.read<AuthUserVM>().setUser(res.data);
        // Update original values after successful save
        final authUserVM = context.read<AuthUserVM>();
        _updateControllers(authUserVM.user);
      },
    );
  }
}
