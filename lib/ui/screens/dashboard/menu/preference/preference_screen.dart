import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class PreferenceScreen extends StatefulWidget {
  const PreferenceScreen({super.key});

  @override
  State<PreferenceScreen> createState() => _PreferenceScreenState();
}

class _PreferenceScreenState extends State<PreferenceScreen> {
  @override
  Widget build(BuildContext context) {
    return Consumer<AuthUserVM>(builder: (context, vm, _) {
      return BusyOverlay(
        show: vm.isBusy,
        child: Scaffold(
          backgroundColor: AppColors.white,
          body: SafeArea(
            child: Container(
              padding: EdgeInsets.symmetric(
                horizontal: Sizer.width(24),
              ).copyWith(
                top: Sizer.height(20),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    alignment: Alignment.centerLeft,
                    child: const ArrowBack(),
                  ),
                  const YBox(30),
                  const AuthTextSubTitle(
                    title: "Preferences",
                    subtitle:
                        "Choose which notifications you want and edit how often you receive them.",
                  ),
                  const YBox(24),
                  Expanded(
                    child: Container(
                      width: Sizer.screenWidth,
                      color: AppColors.white,
                      child: ListView(
                        padding: EdgeInsets.only(
                          top: Sizer.height(24),
                          bottom: Sizer.height(24),
                        ),
                        children: [
                          MenuListTileOLD(
                            title: 'Get Email Notifications',
                            iconData: Iconsax.sms,
                            onPressed: () {},
                            trailingWidget: CustomSwitch(
                              value:
                                  vm.user?.notificationSetting?.email ?? false,
                              onChanged: (value) {
                                printty(value, level: 'Email Switch');
                                vm.updateUserNotification(
                                    {NotificationFields.email: value ? 1 : 0});
                              },
                            ),
                          ),
                          const YBox(20),
                          MenuListTileOLD(
                            title: 'Get Push Notifications',
                            iconData: Iconsax.notification,
                            onPressed: () {},
                            trailingWidget: CustomSwitch(
                              value:
                                  vm.user?.notificationSetting?.push ?? false,
                              onChanged: (value) {
                                printty(value, level: 'Push Switch');
                                vm.updateUserNotification(
                                    {NotificationFields.push: value ? 1 : 0});
                              },
                            ),
                          ),
                          const YBox(20),
                          MenuListTileOLD(
                            title: 'Marketplace Alerts',
                            useImageIcon: true,
                            isSvg: true,
                            imageIcon: AppSvgs.currencyExchange,
                            onPressed: () {
                              FlushBarToast.fLSnackBar(
                                message: 'Marketplace is coming soon',
                                snackBarType: SnackBarType.success,
                              );
                              // Navigator.of(context)
                              //     .pushNamed(RoutePath.setRateAlertScreen);
                            },
                          ),
                          const YBox(20),
                          MenuListTileOLD(
                            title: 'Exchange Rates Alerts',
                            useImageIcon: true,
                            isSvg: true,
                            imageIcon: AppSvgs.currencyExchange,
                            trailingWidget: CustomSwitch(
                              value:
                                  vm.user?.notificationSetting?.rate ?? false,
                              onChanged: (value) {
                                printty(value, level: 'Rate Switch');
                                vm.updateUserNotification(
                                    {NotificationFields.rate: value ? 1 : 0});
                              },
                            ),
                          ),
                          const YBox(20),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      );
    });
  }
}
