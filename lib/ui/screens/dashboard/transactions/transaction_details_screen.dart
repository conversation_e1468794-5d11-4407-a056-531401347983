// ignore_for_file: use_build_context_synchronously

import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class TransactionDetailsScreen extends StatefulWidget {
  const TransactionDetailsScreen({
    super.key,
    required this.transactionArg,
  });

  final TransactionArg transactionArg;

  @override
  State<TransactionDetailsScreen> createState() =>
      _TransactionDetailsScreenState();
}

class _TransactionDetailsScreenState extends State<TransactionDetailsScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  int selectedIndex = 0;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(vsync: this, length: 2);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  bool get isTransfer =>
      widget.transactionArg.transaction.category?.toLowerCase() == "transfer";

  @override
  Widget build(BuildContext context) {
    Transaction transaction = widget.transactionArg.transaction;

    return Scaffold(
      backgroundColor: AppColors.primaryBlue,
      body: SingleChildScrollView(
        physics: const ClampingScrollPhysics(),
        child: Column(
          children: [
            Column(
              children: [
                Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: Sizer.width(24),
                  ),
                  color: AppColors.primaryBlue,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      YBox(70),
                      const CustomHeader(
                        showHeader: true,
                        color: AppColors.white,
                        // headerText: 'Transaction Details',
                      ),
                      const YBox(10),
                      RichText(
                        text: TextSpan(
                          children: [
                            TextSpan(
                              text:
                                  "${transaction.type?.toLowerCase() == "credit" ? "+" : "-"}${AppUtils.formatAmountDoubleString(transaction.amount ?? "")} ",
                              style: AppTypography.text36.copyWith(
                                fontFamily: AppFont.outfit.family,
                                fontWeight: FontWeight.w600,
                                color: AppColors.white,
                              ),
                            ),
                            TextSpan(
                              text: transaction.currency?.code ?? "",
                              style: FontTypography.text20.medium
                                  .withCustomColor(
                                      AppColors.white.withOpacity(0.8)),
                            ),
                          ],
                        ),
                      ),
                      const YBox(4),
                      Text(
                        "${transaction.type?.toLowerCase() == "credit" ? "-" : "+"}${AppUtils.formatAmountDoubleString(transaction.convertedAmount ?? "0")} ${transaction.convertedCurrency?.code ?? ""}",
                        style: FontTypography.text16.medium
                            .withCustomColor(AppColors.blueFE),
                      ),
                      const YBox(65),
                    ],
                  ),
                ),
                Stack(
                  clipBehavior: Clip.none,
                  children: [
                    Container(
                      width: Sizer.screenWidth,
                      decoration: BoxDecoration(
                        color: AppColors.white,
                        borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(Sizer.radius(20)),
                          topRight: Radius.circular(Sizer.radius(20)),
                        ),
                      ),
                      child: Column(
                        children: [
                          Padding(
                            padding: EdgeInsets.symmetric(
                              horizontal: Sizer.width(24),
                            ),
                            child: Column(
                              children: [
                                YBox(55),
                                Center(
                                    child: TransactionDescription(
                                  boldtext: transaction.description ?? '',
                                  // rightText:
                                  //     transaction.category?.toLowerCase() ==
                                  //             "conversion"
                                  //         ? " Conversion"
                                  //         : "",
                                )),
                                const YBox(4),
                                Container(
                                  padding: EdgeInsets.symmetric(
                                    horizontal: Sizer.width(8),
                                    vertical: Sizer.height(4),
                                  ),
                                  decoration: BoxDecoration(
                                    color: getTextColor(widget.transactionArg
                                                .transaction.status ??
                                            "")
                                        .withValues(alpha: 0.1),
                                    borderRadius:
                                        BorderRadius.circular(Sizer.radius(10)),
                                  ),
                                  child: Text(
                                    widget.transactionArg.transaction.status ??
                                        "",
                                    style: FontTypography.text12
                                        .withCustomColor(getTextColor(widget
                                                .transactionArg
                                                .transaction
                                                .status ??
                                            "")),
                                  ),
                                ),
                                const YBox(20),

                                // Tab menu - only show when isTransfer is true
                                if (isTransfer) ...[
                                  Container(
                                    height: Sizer.height(42),
                                    width: Sizer.screenWidth,
                                    decoration: BoxDecoration(
                                      color: AppColors.grayFE,
                                      borderRadius: BorderRadius.circular(
                                          Sizer.radius(60)),
                                      border: Border.all(
                                          width: 1, color: AppColors.grayF0),
                                    ),
                                    child: TabBar(
                                      // splashBorderRadius: BorderRadius.circular(Sizer.radius(12)),
                                      physics:
                                          const NeverScrollableScrollPhysics(),
                                      onTap: (int value) {
                                        selectedIndex = value;
                                        setState(() {});
                                      },
                                      dividerColor: Colors.transparent,
                                      indicator: BoxDecoration(
                                        borderRadius: BorderRadius.circular(
                                            Sizer.radius(60)),
                                        color: AppColors.primaryBlue,
                                      ),
                                      indicatorSize: TabBarIndicatorSize.tab,
                                      labelColor: AppColors.white,
                                      automaticIndicatorColorAdjustment: true,
                                      labelStyle: AppTypography.text14.copyWith(
                                        fontWeight: FontWeight.w500,
                                      ),
                                      unselectedLabelStyle:
                                          AppTypography.text14.copyWith(
                                        color: AppColors.primaryBlue,
                                        fontWeight: FontWeight.w500,
                                      ),
                                      controller: _tabController,
                                      tabs: [
                                        Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.center,
                                          children: [
                                            SvgPicture.asset(
                                              selectedIndex == 0
                                                  ? AppSvgs.messageTextActive
                                                  : AppSvgs.messageText,
                                            ),
                                            const XBox(8),
                                            Text(
                                              'Details',
                                              style:
                                                  AppTypography.text12.copyWith(
                                                fontWeight: FontWeight.w600,
                                                color: selectedIndex == 0
                                                    ? AppColors.white
                                                    : AppColors.primaryBlue,
                                              ),
                                            ),
                                          ],
                                        ),
                                        Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.center,
                                          children: [
                                            SvgPicture.asset(
                                              selectedIndex == 1
                                                  ? AppSvgs.receiptItemActive
                                                  : AppSvgs.receiptItem,
                                            ),
                                            const XBox(8),
                                            Text(
                                              'Timeline',
                                              style:
                                                  AppTypography.text12.copyWith(
                                                fontWeight: FontWeight.w600,
                                                color: selectedIndex == 1
                                                    ? AppColors.white
                                                    : AppColors.primaryBlue,
                                              ),
                                            ),
                                          ],
                                        )
                                      ],
                                    ),
                                  ),
                                  YBox(10),
                                ],

                                // Tab content - conditional based on isTransfer
                                if (isTransfer) ...[
                                  if (selectedIndex == 0)
                                    TransactionDetailsTab(
                                        transaction: transaction),
                                  if (selectedIndex == 1)
                                    TransferTimeTab(
                                      initiatedAt:
                                          AppUtils.formatRelativeDateTime(
                                              transaction.initiatedAt
                                                  ?.toIso8601String()),
                                      completedAt:
                                          AppUtils.formatRelativeDateTime(
                                              transaction.completedAt
                                                  ?.toIso8601String()),
                                      recipient: transaction.recipientName ??
                                          'Recipient',
                                      status:
                                          transaction.status?.toLowerCase() ??
                                              '',
                                    ),
                                ] else ...[
                                  // When not a transfer, always show TransactionDetailsTab
                                  TransactionDetailsTab(
                                      transaction: transaction),
                                ],
                              ],
                            ),
                          ),
                          YBox(100)
                        ],
                      ),
                    ),
                    Positioned(
                      top: -40,
                      left: 0,
                      right: 0,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Column(
                            children: [
                              SizedBox(
                                height: Sizer.height(88),
                                width: Sizer.width(88),
                                child: svgHelper(
                                  _getSvg(),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
      bottomSheet: Container(
        padding: EdgeInsets.only(
          top: Sizer.height(10),
          bottom: Sizer.height(30),
          left: Sizer.width(24),
          right: Sizer.width(24),
        ),
        decoration: BoxDecoration(
          color: AppColors.white,
          border: Border(
            top: BorderSide(
              color: AppColors.grayF4,
              width: 1,
            ),
          ),
        ),
        child: transaction.category?.toLowerCase() == "conversion"
            ? CustomBtn.solid(
                borderRadius: BorderRadius.circular(20),
                onTap: () {
                  final isCredit = transaction.type?.toLowerCase() == "credit";
                  final fromAmount = isCredit
                      ? transaction.convertedAmount
                      : transaction.amount;
                  // final toAmount = isCredit
                  //     ? transaction.amount
                  //     : transaction.convertedAmount;
                  final fromCurrencyCode = isCredit
                      ? transaction.convertedCurrency?.code
                      : transaction.currency?.code;
                  final toCurrencyCode = isCredit
                      ? transaction.currency?.code
                      : transaction.convertedCurrency?.code;

                  Navigator.pushNamed(
                    context,
                    RoutePath.convertCurrencyScreen,
                    arguments: ConvertArg(
                      fromAmount: fromAmount,
                      // toAmount: toAmount, // This will be calculated on convert screen cus of rate change
                      fromCurrencyCode: fromCurrencyCode,
                      toCurrencyCode: toCurrencyCode,
                    ),
                  );
                },
                text: "Convert Again",
              )
            : Row(
                children: [
                  Expanded(
                    flex: 2,
                    child: InkWell(
                      onTap: () {
                        final fromCurrencyCode = transaction.currency?.code;
                        final toCurrencyCode =
                            transaction.convertedCurrency?.code ??
                                fromCurrencyCode;

                        printty("fromCurrencyCode: $fromCurrencyCode");
                        printty("toCurrencyCode: $toCurrencyCode");

                        Navigator.pushNamed(
                          context,
                          RoutePath.sendMoneyScreen,
                          arguments: SendMoneyArg(
                            fromAmount: transaction.amount,
                            fromCurrencyCode: transaction.currency?.code ?? '',
                            toCurrencyCode: toCurrencyCode ?? '',
                            beneficiary: transaction.beneficiary,
                            purpose: transaction.purpose,
                          ),
                        );
                      },
                      child: Container(
                        height: Sizer.height(44),
                        decoration: BoxDecoration(
                          border: Border.all(
                            color: AppColors.primaryBlue,
                            width: 1,
                          ),
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Center(
                          child: Text(
                            'Send Again',
                            style: FontTypography.text14.medium.withCustomColor(
                              AppColors.primaryBlue,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                  if (transaction.status?.toLowerCase() != "pending")
                    Expanded(
                      flex: 3,
                      child: Row(
                        children: [
                          Expanded(
                            child: InkWell(
                              onTap: () {
                                // BsWrapper.bottomSheet(
                                //     context: context,
                                //     widget:
                                //         RateExperienceModal());
                                Navigator.pushNamed(
                                  context,
                                  RoutePath.transactionReceiptScreen,
                                  arguments: ReceiptArg(
                                    transaction: transaction,
                                    isDownload: true,
                                  ),
                                );
                              },
                              child: Container(
                                margin: EdgeInsets.only(
                                  left: Sizer.width(16),
                                ),
                                decoration: BoxDecoration(
                                  color: AppColors.blue9FF,
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                height: Sizer.height(44),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    svgHelper(
                                      AppSvgs.download,
                                      color: AppColors.primaryBlue,
                                      height: Sizer.height(24),
                                      width: Sizer.width(24),
                                    ),
                                    const XBox(12),
                                    Text(
                                      'Download',
                                      style: FontTypography.text14.medium
                                          .withCustomColor(
                                        AppColors.primaryBlue,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                          const XBox(10),
                          InkWell(
                            onTap: () async {
                              Navigator.pushNamed(
                                context,
                                RoutePath.transactionReceiptScreen,
                                arguments: ReceiptArg(
                                  transaction: transaction,
                                ),
                              );
                            },
                            child: Container(
                              padding: EdgeInsets.symmetric(
                                horizontal: Sizer.width(12),
                                vertical: Sizer.height(10),
                              ),
                              decoration: BoxDecoration(
                                color: AppColors.primaryBlue,
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: svgHelper(
                                AppSvgs.share,
                                color: AppColors.white,
                              ),
                            ),
                          ),
                        ],
                      ),
                    )
                ],
              ),
      ),
    );
  }

  _getSvg() {
    var category = widget.transactionArg.transaction.category?.toLowerCase();
    var type = widget.transactionArg.transaction.type?.toLowerCase();
    switch (type) {
      case "credit":
        if (category == "p2p" || category == "conversion") {
          return AppSvgs.conversion;
        }
        return AppSvgs.transfer;
      case "debit":
        if (category == "p2p" || category == "conversion") {
          return AppSvgs.conversion;
        }
        return AppSvgs.transfer;
      default:
        return AppSvgs.transfer;
    }
  }

  // _getTextColor(String status) {
  //   switch (status) {
  //     case "successful":
  //       return AppColors.iconGreen;
  //     case "pending":
  //       return AppColors.pending;
  //     default:
  //       return AppColors.failed;
  //   }
  // }

  // Future<bool> _requestPermissions() async {
  //   if (Platform.isAndroid) {
  //     final androidInfo = await DeviceInfoPlugin().androidInfo;
  //     final sdkInt = androidInfo.version.sdkInt;

  //     if (sdkInt >= 33) {
  //       // Android 13+
  //       final status = await Permission.photos.request();
  //       return status.isGranted;
  //     } else if (sdkInt >= 30) {
  //       // Android 11-12
  //       final status = await Permission.storage.request();
  //       return status.isGranted;
  //     } else {
  //       // Android 10 and below
  //       final status = await Permission.storage.request();
  //       return status.isGranted;
  //     }
  //   } else if (Platform.isIOS) {
  //     final status = await Permission.photos.request();
  //     return status.isGranted;
  //   }

  //   return false;
  // }
}

class TransactionDescription extends StatelessWidget {
  const TransactionDescription({
    super.key,
    this.leftText,
    required this.boldtext,
    this.rightText,
  });
  final String? leftText;
  final String boldtext;
  final String? rightText;

  @override
  Widget build(BuildContext context) {
    return RichText(
      text: TextSpan(
        children: [
          if (leftText != null)
            TextSpan(
              text: leftText,
              style: FontTypography.text16.withCustomColor(AppColors.gray79),
            ),
          TextSpan(
            text: boldtext,
            style: FontTypography.text16.bold.withCustomColor(AppColors.gray79),
          ),
          if (rightText != null)
            TextSpan(
              text: rightText,
              style: FontTypography.text16.withCustomColor(AppColors.gray79),
            ),
        ],
      ),
    );
  }
}
