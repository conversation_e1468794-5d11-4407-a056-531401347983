import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class OtherWalletDetailsScreen extends StatefulWidget {
  const OtherWalletDetailsScreen({super.key, required this.wallet});

  final Wallet wallet;

  @override
  State<OtherWalletDetailsScreen> createState() =>
      _OtherWalletDetailsScreenState();
}

class _OtherWalletDetailsScreenState extends State<OtherWalletDetailsScreen> {
  bool _hideAccountLimits = false;
  bool _hideWalletAmount = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final currencyId = widget.wallet.currency?.id;
      context.read<CurrencyVM>().getCurrenciesLimits(currencyId: currencyId);
      context.read<BeneficiaryVM>().getWalletFreqBeneficiaries(
            currencyId: currencyId ?? 0,
          );
      context.read<TransactionVM>().getFilteredTransactions(
            isAllTransaction: false,
            currencyId: currencyId,
          );
    });
  }

  @override
  Widget build(BuildContext context) {
    final walletVm = context.watch<WalletVM>();
    final currencyVm = context.watch<CurrencyVM>();
    final beneficiaryVm = context.watch<BeneficiaryVM>();
    // printty("Wallet ${widget.wallet.currency?.name}");
    return BusyOverlay(
      show: currencyVm.isBusy,
      child: Scaffold(
        appBar: NewCustomAppbar(
          showHeaderTitle: true,
          headerText: 'Wallet Details',
          textSize: Sizer.text(16),
          onBackBtnTap: () {
            Navigator.pop(context);
          },
        ),
        body: ListView(
          padding: EdgeInsets.only(
            top: Sizer.height(24),
            bottom: Sizer.height(80),
          ),
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Align(
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(100),
                    child: SizedBox(
                      width: Sizer.width(48),
                      height: Sizer.height(48),
                      child: SvgPicture.network(
                        widget.wallet.currency?.flag ?? "",
                        fit: BoxFit.cover,
                      ),
                    ),
                  ),
                ),
                YBox(12),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      widget.wallet.currency?.name ?? "",
                      style: AppTypography.text17.medium
                          .copyWith(color: AppColors.mainBlack),
                    ),
                    XBox(8),
                    InkWell(
                      onTap: () async {
                        final res = await BsWrapper.bottomSheet(
                          context: context,
                          widget: SelectWalletModal(
                            exportWallet: true,
                          ),
                        );

                        if (res is Wallet) {
                          // Check if its same wallet
                          if (res.id == widget.wallet.id) {
                            return;
                          }
                          if (context.mounted) {
                            Navigator.pop(context);
                            navigateToWalletScreen(
                              context: context,
                              currencyCode: res.currency?.code ?? '',
                              wallet: res,
                            );
                          }
                        }
                      },
                      child: SvgPicture.asset(
                        AppSvgs.arrowDown,
                        width: Sizer.width(24),
                        height: Sizer.height(24),
                      ),
                    ),
                  ],
                ),
                YBox(4),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    _hideWalletAmount
                        ? Text(
                            "*** *** ***",
                            style: AppTypography.text32.semiBold.copyWith(
                              color: AppColors.mainBlack,
                              fontFamily: AppFont.outfit.family,
                            ),
                          )
                        : RichText(
                            text: TextSpan(
                              children: [
                                TextSpan(
                                  text: widget.wallet.currency?.symbol ?? "",
                                  style: AppTypography.text32.semiBold.copyWith(
                                    color: AppColors.mainBlack,
                                    fontFamily: AppFont.inter.family,
                                  ),
                                ),
                                TextSpan(
                                  text: walletVm.homeBalance(widget.wallet),
                                  style: AppTypography.text32.semiBold.copyWith(
                                    color: AppColors.mainBlack,
                                    fontFamily: AppFont.outfit.family,
                                  ),
                                ),
                                TextSpan(
                                  text: ".",
                                  style: AppTypography.text32.medium.copyWith(
                                    color: AppColors.mainBlack,
                                    fontFamily: AppFont.outfit.family,
                                  ),
                                ),
                                TextSpan(
                                  text: walletVm.decimalBalance(widget.wallet),
                                  style: AppTypography.text20.semiBold.copyWith(
                                    color: AppColors.mainBlack,
                                    fontFamily: AppFont.outfit.family,
                                  ),
                                ),
                              ],
                            ),
                          ),
                    XBox(10),
                    InkWell(
                      onTap: () {
                        _hideWalletAmount = !_hideWalletAmount;
                        setState(() {});
                      },
                      child: SvgPicture.asset(
                        _hideWalletAmount
                            ? AppSvgs.walletEyeSlash
                            : AppSvgs.walletEye,
                        height: Sizer.height(28),
                      ),
                    ),
                  ],
                ),
                YBox(30),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    WalletColTab(
                      text: "Add",
                      iconPath: AppSvgs.request,
                    ),
                    XBox(26),
                    WalletColTab(
                      text: "Convert",
                      iconPath: AppSvgs.convert,
                      onTap: () {
                        Navigator.pushNamed(
                            context, RoutePath.convertCurrencyScreen);
                      },
                    ),
                    XBox(26),
                    WalletColTab(
                      text: "Send",
                      iconPath: AppSvgs.walletSend,
                      onTap: () {
                        final fromCurrencyCode =
                            widget.wallet.currency?.code ?? '';
                        Navigator.pushNamed(
                          context,
                          RoutePath.sendMoneyScreen,
                          arguments: SendMoneyArg(
                            fromCurrencyCode: fromCurrencyCode,
                            toCurrencyCode: "",
                            fromWallet: widget.wallet,
                          ),
                        );
                      },
                    ),
                    XBox(26),
                    WalletColTab(
                      text: "More",
                      iconPath: AppSvgs.more,
                      onTap: () {
                        BsWrapper.bottomSheet(
                          context: context,
                          widget: WalletMoreOptionsModal(
                            wallet: widget.wallet,
                          ),
                        );
                      },
                    ),
                  ],
                ),
                YBox(40),
                HeaderHideToggle(
                  header: "Account Limits",
                  hidden: _hideAccountLimits,
                  onTap: () {
                    _hideAccountLimits = !_hideAccountLimits;
                    setState(() {});
                  },
                ),
                AnimatedSize(
                  duration: const Duration(milliseconds: 500),
                  curve: Curves.easeInOut,
                  child: _hideAccountLimits
                      ? SizedBox.shrink()
                      : Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: Sizer.width(16),
                            vertical: Sizer.height(20),
                          ),
                          margin: EdgeInsets.symmetric(
                            horizontal: Sizer.width(24),
                          ),
                          decoration: BoxDecoration(
                            color: AppColors.blueBFF,
                            borderRadius:
                                BorderRadius.circular(Sizer.radius(16)),
                          ),
                          child: Column(
                            children: buildAccountLimitCards(
                              limits: currencyVm.getCurrencyLimit(
                                  widget.wallet.currency?.code ?? ''),
                              currency: currencyVm.nairaCurrency,
                            ),
                          ),
                        ),
                ),
                YBox(28),
                Consumer<TransactionVM>(
                    builder: (context, transactionVm, child) {
                  final transactionList =
                      transactionVm.transactionByCurrencyId.take(3).toList();
                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      HeaderHideToggle(
                        header: "Transactions",
                        trailingText: "View all",
                        onTap: transactionList.isNotEmpty
                            ? () {
                                Navigator.pushNamed(
                                    context, RoutePath.dashboardNav,
                                    arguments: 1);
                              }
                            : null,
                      ),
                      ListView.separated(
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        padding: EdgeInsets.symmetric(
                          horizontal: Sizer.width(24),
                        ),
                        itemCount: transactionList.length,
                        separatorBuilder: (_, __) =>
                            HDivider(verticalPadding: 16),
                        itemBuilder: (ctx, i) {
                          return TransactionListTile(
                            title: transactionList[i].title ?? "N/A",
                            subTitle: transactionList[i].subtitle ?? "N/A",
                            amount:
                                "${AppUtils.formatAmountDoubleString(transactionList[i].amount ?? "0")} ${transactionList[i].currency?.code ?? ""}",
                            status: transactionList[i].status ?? '',
                            category: transactionList[i].category ?? '',
                            onTap: () {
                              Navigator.of(context).pushNamed(
                                  RoutePath.transactionDetailsScreen,
                                  arguments: TransactionArg(
                                      transaction: transactionList[i]));
                            },
                          );
                        },
                      ),
                    ],
                  );
                }),
                YBox(28),
                HeaderHideToggle(
                  header: "Send again",
                ),
                LoadableContentBuilder(
                  isBusy: beneficiaryVm.isBusy,
                  items: beneficiaryVm.walletFreqBeneficiaries,
                  loadingBuilder: (ctx) {
                    return SingleChildScrollView(
                      scrollDirection: Axis.horizontal,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: List.generate(
                          5,
                          (i) => Padding(
                            padding: EdgeInsets.only(
                              left: i == 0 ? Sizer.width(24) : 0,
                              right: Sizer.width(12),
                            ),
                            child: Skeletonizer(
                              enabled: true,
                              child: QuickSendWidget(
                                beneficiaryName: "Beneficiary Name",
                                currencyFlag: AppUtils.kCadFlag,
                              ),
                            ),
                          ),
                        ),
                      ),
                    );
                  },
                  emptyBuilder: (ctx) {
                    return Center(
                      child: EmptyState(
                        title: "No beneficiary found",
                      ),
                    );
                  },
                  contentBuilder: (context) {
                    return SingleChildScrollView(
                      scrollDirection: Axis.horizontal,
                      child: Row(
                        children: List.generate(
                            beneficiaryVm.walletFreqBeneficiaries.length, (i) {
                          final beneficiary =
                              beneficiaryVm.walletFreqBeneficiaries[i];
                          return Padding(
                            padding: EdgeInsets.only(
                              left: i == 0 ? Sizer.width(24) : 0,
                              right: Sizer.width(12),
                            ),
                            child: QuickSendWidget(
                              beneficiaryName: beneficiary.accountName ?? "",
                              currencyFlag: beneficiary.currency?.flag ?? "",
                              onTap: () {
                                final fromCurrencyCode = beneficiary
                                        .latestTransaction?.currency?.code ??
                                    "";
                                final toCurrencyCode = beneficiary
                                        .latestTransaction
                                        ?.receivedCurrency
                                        ?.code ??
                                    "";
                                Navigator.pushNamed(
                                  context,
                                  RoutePath.sendMoneyScreen,
                                  arguments: SendMoneyArg(
                                    fromCurrencyCode: fromCurrencyCode,
                                    toCurrencyCode: toCurrencyCode,
                                    fromAmount:
                                        beneficiary.latestTransaction?.amount ??
                                            "",
                                    beneficiary: beneficiary,
                                  ),
                                );
                              },
                            ),
                          );
                        }),
                      ),
                    );
                  },
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
