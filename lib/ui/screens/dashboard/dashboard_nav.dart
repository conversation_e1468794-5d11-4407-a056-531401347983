import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';
import 'package:korrency/ui/screens/screens.dart';

class DashboardArg {
  final int index;
  DashboardArg({this.index = 0});
}

class DashboardNav extends StatefulWidget {
  const DashboardNav({
    super.key,
    this.index,
  });

  final int? index;

  @override
  State<DashboardNav> createState() => _DashboardNavState();
}

class _DashboardNavState extends State<DashboardNav> {
  List screens = [
    const HomeScreen(),
    const TransactionsScreen(),
    const ExchangeScreen(),
    // const NavigateP2pMarketplace(),
    const RewardsScreen(),
    const MenuScreen(),
    const YourOfferScreen(),
  ];

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (widget.index != null) {
        printty("Index ${widget.index}");
        context.read<DashboardVM>().changeScreenIndex(widget.index ?? 0);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      child: Consumer<DashboardVM>(builder: (context, vm, _) {
        return Scaffold(
          body: screens[vm.currentIndex],
          backgroundColor: AppColors.bgWhite,
          bottomNavigationBar: Container(
            height: Sizer.height(84),
            padding: EdgeInsets.only(
              bottom: Sizer.height(10),
            ),
            decoration: BoxDecoration(
              color: AppColors.bgWhite,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(Sizer.radius(32)),
                topRight: Radius.circular(Sizer.radius(32)),
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black12.withValues(alpha: 0.06),
                  blurRadius: 10,
                  spreadRadius: 0,
                  offset: const Offset(0, -2),
                ),
              ],
            ),
            child: Consumer<AuthUserVM>(builder: (context, authVM, _) {
              return Padding(
                padding: EdgeInsets.symmetric(
                  horizontal: Sizer.width(20),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    BottomNavColumn(
                      isSelected: vm.currentIndex == 0,
                      icon: vm.currentIndex == 0 ? AppSvgs.homeB : AppSvgs.home,
                      labelText: 'Home',
                      onPressed: () {
                        vm.changeScreenIndex(0);
                      },
                    ),
                    BottomNavColumn(
                      isSelected: vm.currentIndex == 1,
                      icon: vm.currentIndex == 1
                          ? AppSvgs.analyticsB
                          : AppSvgs.analytics,
                      labelText: 'Transactions',
                      onPressed: () {
                        vm.changeScreenIndex(1);
                      },
                    ),
                    // BottomNavColumn(
                    //   icon: vm.currentIndex == 2
                    //       ? AppSvgs.shareB
                    //       : AppSvgs.exchange,
                    //   labelText: 'Exchange',
                    //   fontWeight: vm.currentIndex == 2
                    //       ? FontWeight.w700
                    //       : FontWeight.normal,
                    //   onPressed: () {
                    //     if (authVM.statusProcessing) {
                    //       FlushBarToast.fLSnackBar(
                    //         message: 'Your ID verification is in progress',
                    //         snackBarType: SnackBarType.success,
                    //       );
                    //       return;
                    //     }
                    //     vm.changeScreenIndex(2);
                    //   },
                    // ),
                    // BottomNavColumn(
                    //   icon: vm.currentIndex == 3 ? AppSvgs.shopB : AppSvgs.shop,
                    //   labelText: 'Marketplace',
                    //   fontWeight: vm.currentIndex == 3
                    //       ? FontWeight.w700
                    //       : FontWeight.normal,
                    //   onPressed: () {
                    //     FlushBarToast.fLSnackBar(
                    //       message: 'Marketplace is coming soon',
                    //       snackBarType: SnackBarType.success,
                    //     );
                    //     // if (!authVM.userIsVerified) {
                    //     //   return BsWrapper.bottomSheet(
                    //     //     canDismiss: false,
                    //     //     context: context,
                    //     //     widget: const CompleteKycScreen(),
                    //     //   );
                    //     // }
                    //     // vm.changeScreenIndex(3);
                    //   },
                    // ),
                    BottomNavColumn(
                      isSelected: vm.currentIndex == 3,
                      icon: vm.currentIndex == 3
                          ? AppSvgs.navGiftB
                          : AppSvgs.navGift,
                      labelText: 'Rewards',
                      onPressed: () {
                        vm.changeScreenIndex(3);
                      },
                    ),
                    BottomNavColumn(
                      isSelected: vm.currentIndex == 4,
                      icon: vm.currentIndex == 4 ? AppSvgs.userB : AppSvgs.user,
                      labelText: 'Me',
                      onPressed: () {
                        vm.changeScreenIndex(4);
                      },
                    ),
                  ],
                ),
              );
            }),
          ),
        );
      }),
    );
  }
}
