package korrency.mobile.com

import io.flutter.embedding.android.FlutterFragmentActivity
import io.flutter.embedding.android.FlutterActivity


import android.content.Intent
import android.os.Bundle
import android.media.MediaScannerConnection
import android.net.Uri
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel
import java.io.File

class MainActivity: FlutterFragmentActivity() {  // Changed from FlutterActivity() to FlutterFragmentActivity()
    private val CHANNEL = "media_scanner"

    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)
        
        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL).setMethodCallHandler { call, result ->
            when (call.method) {
                "scanFile" -> {
                    val path = call.argument<String>("path")
                    if (path != null) {
                        scanFile(path)
                        result.success("File scanned")
                    } else {
                        result.error("INVALID_PATH", "File path is null", null)
                    }
                }
                else -> {
                    result.notImplemented()
                }
            }
        }
    }

    private fun scanFile(filePath: String) {
        val file = File(filePath)
        if (file.exists()) {
            // Method 1: Using MediaScannerConnection
            MediaScannerConnection.scanFile(
                this,
                arrayOf(filePath),
                null
            ) { path, uri ->
                // File has been scanned and added to MediaStore
            }
            
            // Method 2: Broadcasting intent (fallback)
            val intent = Intent(Intent.ACTION_MEDIA_SCANNER_SCAN_FILE)
            intent.data = Uri.fromFile(file)
            sendBroadcast(intent)
        }
    }

   override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        // Handle intent if the app was started from a deep link
        if (intent != null) {
            handleIntent(intent)
        }
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        // Update the intent
        setIntent(intent)
        // Handle the new intent
        handleIntent(intent)
    }

    private fun handleIntent(intent: Intent) {
        // The actual deep link handling is done by the Flutter code and AppsFlyer SDK
        // This is just to ensure the intent is properly passed to the Flutter layer
        val action = intent.action
        val data = intent.data
        if (Intent.ACTION_VIEW == action && data != null) {
            // Log that we received a deep link intent
            println("Deep link received in MainActivity: ${data.toString()}")
        }
    }
}